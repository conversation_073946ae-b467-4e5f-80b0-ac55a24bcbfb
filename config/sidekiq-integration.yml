:concurrency: 15

development:
  :verbose: true

:queues:
  - [webhook, 3]
  - [elasticsearch_integration, 2]
  - [elasticsearch_setup, 1]

#cron parser: https://github.com/floraison/fugit
:scheduler:
  :schedule:
    delete_unused_drafts:
      cron: "00 01 * * *" # every day at 01h
      class: DeleteUnusedDraftsWorker
    delete_old_troubleshootings:
      cron: "00 01 * * *" # every day at 01h
      class: DeleteOldTroubleshootingsWorker
    update_cloudwatch_queue_statistics:
      every: 2 minutes
      class: UpdateCloudwatchQueueStatisticsWorker
    reprocess_incomplete_bulk_saving_answers:
      every: 30 minutes
      class: ReprocessIncompleteBulkSavingAnswersWorker
    check_not_indexed_contents:
      every: 60 minutes
      class: CheckOutdatedContentsInElasticsearchWorker
    check_not_indexed_businesses_keys:
      cron: "00 01 * * *" # every day at 01h
      class: CheckNotIndexedBusinessesKeysWorker
