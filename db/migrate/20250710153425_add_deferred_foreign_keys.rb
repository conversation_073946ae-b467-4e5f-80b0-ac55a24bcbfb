class AddDeferredForeignKeys < ActiveRecord::Migration[7.1]
  def change
    reversible do |dir|
      dir.up do
        remove_foreign_key :contents, :answers, column: :current_answer_id, if_exists: true
        remove_foreign_key :contents, :contents, column: :parent_id, if_exists: true
        remove_foreign_key :steps, :steps, column: :step_for_revision_id, if_exists: true

        add_foreign_key :contents, :answers, column: :current_answer_id, deferrable: :deferred
        add_foreign_key :contents, :contents, column: :parent_id, deferrable: :deferred
        add_foreign_key :steps, :steps, column: :step_for_revision_id, deferrable: :deferred
      end

      dir.down do
        remove_foreign_key :contents, :answers, column: :current_answer_id, if_exists: true
        remove_foreign_key :contents, :contents, column: :parent_id, if_exists: true
        remove_foreign_key :steps, :steps, column: :step_for_revision_id, if_exists: true

        add_foreign_key :contents, :answers, column: :current_answer_id
        add_foreign_key :contents, :contents, column: :parent_id
        add_foreign_key :steps, :steps, column: :step_for_revision_id
      end
    end
  end
end
