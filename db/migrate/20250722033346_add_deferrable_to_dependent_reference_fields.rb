
  class AddDeferrableToDependentReferenceFields < ActiveRecord::Migration[7.1]
  def change
    reversible do |dir|
      dir.up do
        # Remove existing foreign keys
        remove_foreign_key :dependent_reference_fields, :steps, column: :step_id, if_exists: true
        remove_foreign_key :dependent_reference_fields, :fields, column: :field_id, if_exists: true
        remove_foreign_key :dependent_reference_fields, :fields, column: :parent_field_id, if_exists: true
        remove_foreign_key :dependent_reference_fields, :steps, column: :parent_step_id, if_exists: true

        # Add foreign keys with deferrable option
        add_foreign_key :dependent_reference_fields, :steps, column: :step_id, deferrable: :immediate
        add_foreign_key :dependent_reference_fields, :fields, column: :field_id, deferrable: :immediate
        add_foreign_key :dependent_reference_fields, :fields, column: :parent_field_id, deferrable: :immediate
        add_foreign_key :dependent_reference_fields, :steps, column: :parent_step_id, deferrable: :immediate
      end

      dir.down do
        # Remove deferrable foreign keys
        remove_foreign_key :dependent_reference_fields, :steps, column: :step_id, if_exists: true
        remove_foreign_key :dependent_reference_fields, :fields, column: :field_id, if_exists: true
        remove_foreign_key :dependent_reference_fields, :fields, column: :parent_field_id, if_exists: true
        remove_foreign_key :dependent_reference_fields, :steps, column: :parent_step_id, if_exists: true

        # Add back regular foreign keys
        add_foreign_key :dependent_reference_fields, :steps, column: :step_id
        add_foreign_key :dependent_reference_fields, :fields, column: :field_id
        add_foreign_key :dependent_reference_fields, :fields, column: :parent_field_id
        add_foreign_key :dependent_reference_fields, :steps, column: :parent_step_id
      end
    end
  end
end
