namespace :contents do
  task delete_invalid: :environment do
    def destroy_permanently(content_id)
      Content.unscoped.children_of(content_id).select(:id).find_each { |id| destroy_permanently(id) }

      ActiveRecord::Base.transaction(requires_new: true) do
        content = Content.unscoped.find_by_id(content_id)
        content.update_columns(current_answer_id: nil, parent_id: nil)

        content.content_values.delete_all

        AnswerProcessing.where(answer_id: content.answer_ids).update_all(answer_id: nil)
        content.answers.each {|answer| answer.versions.delete_all }
        content.answers.delete_all

        FieldOption.where(content_id: content.id).delete_all

        content.destroy
      end
    end

    Company.all.each do |company|
      Apartment::Tenant.switch! company.subdomain

      contents_without_parents_sql = <<~SQL.squish
        select c.id
        from contents c
          left join contents c2 on c2.id = c.parent_id
        where c2.id is null
          and c.parent_id is not null
      SQL

      contents_with_invalid_current_answer_sql = <<~SQL.squish
        select c.id
        from contents c
          left join answers a on a.id = c.current_answer_id
        where a.id is null
          and c.current_answer_id is not null
      SQL

      ids_to_delete = Set.new()

      ActiveRecord::Base.connection.exec_query(contents_without_parents_sql).each do |row|
        ids_to_delete << row['id']
      end

      ActiveRecord::Base.connection.exec_query(contents_with_invalid_current_answer_sql).each do |row|
        ids_to_delete << row['id']
      end

      puts "Deleting #{ids_to_delete.size} contents from #{company.subdomain}"

      ids_to_delete.each do |id|
        destroy_permanently(id)
      end
    end
  end
end
