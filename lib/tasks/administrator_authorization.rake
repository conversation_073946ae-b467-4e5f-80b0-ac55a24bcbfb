namespace :administrators do
  task :generate_token, [:update_all] => :environment do |t, args|
    def schema_exists?(schema_name)
      query = <<-SQL
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name = '#{schema_name}'
      SQL

      result = ActiveRecord::Base.connection.execute(query).to_a
      result.any?
    end

    update_all = args[:update_all] == 'update_all'
    Company.find_each do |company|
      schema = company.subdomain

      if schema_exists?(schema)
        Apartment::Tenant.switch! schema
        administrators = update_all ? Administrator.all : Administrator.where(authorization_token: nil)

        puts "Gerando tokens para #{administrators.count} usuários admin em '#{schema}'"
        administrators.find_each do |administrator|
          administrator.authorization_token = administrator.generate_token
          administrator.save
        end
        puts 'Tokens gerados!'
      else
        puts "Schema '#{schema}' não encontrado. Pulando."
      end
    end
  end
end
