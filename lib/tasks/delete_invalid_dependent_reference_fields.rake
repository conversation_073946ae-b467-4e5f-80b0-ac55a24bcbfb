namespace :dependent_reference_fields do
  task delete_invalid: :environment do
    puts "Cleaning orphaned dependent_reference_fields..."

    total_deleted = 0
    start_time = Time.current

    Company.all.each do |company|
      Apartment::Tenant.switch! company.subdomain

      # Single SQL query to find all orphaned dependent_reference_fields
      orphaned_dependent_reference_fields_sql = <<~SQL.squish
        SELECT drf.id
        FROM dependent_reference_fields drf
          LEFT JOIN steps s1 ON s1.id = drf.step_id
          LEFT JOIN steps s2 ON s2.id = drf.parent_step_id
          LEFT JOIN fields f1 ON f1.id = drf.field_id
          LEFT JOIN fields f2 ON f2.id = drf.parent_field_id
        WHERE (s1.id IS NULL AND drf.step_id IS NOT NULL)
           OR (s2.id IS NULL AND drf.parent_step_id IS NOT NULL)
           OR (f1.id IS NULL AND drf.field_id IS NOT NULL)
           OR (f2.id IS NULL AND drf.parent_field_id IS NOT NULL)
      SQL

      orphaned_records = ActiveRecord::Base.connection
        .exec_query(orphaned_dependent_reference_fields_sql)
        .map { |row| "'#{row['id']}'" }

      if orphaned_records.any?
        # Single SQL deletion
        delete_sql = "DELETE FROM dependent_reference_fields WHERE id IN (#{orphaned_records.join(', ')})"

        ActiveRecord::Base.transaction do
          deleted_count = ActiveRecord::Base.connection.execute(delete_sql).cmd_tuples
          total_deleted += deleted_count
          puts "  Deleted #{deleted_count} records from #{company.subdomain}"
        end
      else
        puts "  No orphaned records found in #{company.subdomain}"
      end
    end

    elapsed_time = Time.current - start_time
    puts "\nCleanup completed!"
    puts "Total deleted: #{total_deleted} records in #{elapsed_time.round(2)}s"
  end
end
