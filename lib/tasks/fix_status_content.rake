namespace :status_content do
  task :fix_status_content, [:tenant] => :environment do |t, args|
    companies = args[:tenant].present? ? [Company.find_by(subdomain: args[:tenant])] : Company.all

    companies.find_each do |company|
      Apartment::Tenant.switch! company.subdomain

      puts "Atualizando status_content para a empresa #{company.subdomain}"

      update_sql = <<-SQL.squish
        with contents_to_update as (
          select
            c.id content_id, c.business_id,
            a2.new_content_status,
            a2.id answer_id
          from contents c
            cross join lateral (
              select
                id,
                content_id,
                case status
                  when 0 then 0  /* pending → pending */
                  when 2 then 1  /* waiting_authorization → waiting_authorization */
                  when 3 then 5  /* rejected → rejected */
                  when 4 then 3  /* changing → changing */
                  when 5 then 4  /* under_review → under_review */
                end new_content_status
              from answers a
              where status != 1
                and c.id = a.content_id
              order by position
              limit 1
            ) a2
        )
        update contents c
        set
          status = cu.new_content_status,
          current_answer_id = cu.answer_id
        from contents_to_update cu
        where c.id = cu.content_id
          and (
            c.status != cu.new_content_status
            or c.current_answer_id != cu.answer_id
          )
        ;
      SQL

      affected_rows = ActiveRecord::Base.connection.update(update_sql)

      puts "Progresso de #{affected_rows} conteúdos atualizados com sucesso para o tenant #{company.subdomain}"
    end
  end
end
