namespace :dependent_field_rules do
  desc "Fix dependent field rule values by transforming object arrays to simple value arrays"
  task fix_dependent_field_rule_values: :environment do
    Company.all.each do |company|
      Apartment::Tenant.switch! company.subdomain

      sql = <<~SQL.squish
          WITH RECURSIVE json_rules AS (
              SELECT
                  dfr.id,
                  jsonb_array_elements(dfr.rules->'rules') AS rule_json,
                  jsonb_array_elements(dfr.rules->'rules')->'rules' AS nested_rules_json
              FROM testes.dependent_field_rules dfr

              UNION ALL

              SELECT
                  jr.id,
                  jsonb_array_elements(jr.nested_rules_json) AS rule_json,
                  jsonb_array_elements(jr.nested_rules_json)->'rules' AS nested_rules_json
              FROM json_rules jr
              WHERE jr.nested_rules_json IS NOT NULL
                AND jsonb_typeof(jr.nested_rules_json) = 'array'
          )
          SELECT DISTINCT dfr.*
          FROM testes.dependent_field_rules dfr
          JOIN json_rules jr ON jr.id = dfr.id
          WHERE jsonb_typeof(jr.rule_json->'value') = 'array'
            AND jsonb_typeof((jr.rule_json->'value')->0) = 'object';
          SQL

      dependent_field_rules = DependentFieldRule.find_by_sql(sql)

      puts "Found #{dependent_field_rules.count} dependent field rules to fix in #{company.subdomain}"

      dependent_field_rules.each do |rule|
        transformed_rules = transform_rule_values(rule.rules)
        rule.update!(rules: transformed_rules)
      end
    end
  end

  private

  def transform_rule_values(rules_hash)
    return rules_hash unless rules_hash.is_a?(Hash)

    transformed = rules_hash.deep_dup

    if transformed['rules'].is_a?(Array)
      transformed['rules'] = transformed['rules'].map do |rule|
        transform_single_rule(rule)
      end
    end

    transformed
  end

  def transform_single_rule(rule)
    return rule unless rule.is_a?(Hash)

    transformed_rule = rule.deep_dup

    if transformed_rule['value'].is_a?(Array) && transformed_rule['value'].all? { |item| item.is_a?(Hash) && item.key?('value') }
      transformed_rule['value'] = transformed_rule['value'].map { |item| item['value'] }
    end

    if transformed_rule['rules'].is_a?(Array)
      transformed_rule['rules'] = transformed_rule['rules'].map do |nested_rule|
        transform_single_rule(nested_rule)
      end
    end

    transformed_rule
  end
end
