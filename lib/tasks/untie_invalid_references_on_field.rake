namespace :fields do
  task untie_invalid_references: :environment do
    puts "Cleaning orphaned references on fields..."

    total_updated = 0
    start_time = Time.current

    Company.all.each do |company|
      Apartment::Tenant.switch! company.subdomain

      # Single SQL query to find fields with orphaned references
      orphaned_fields_sql = <<~SQL.squish
        SELECT f.id
        FROM fields f
          LEFT JOIN businesses rb ON rb.id = f.reference_business_id
          LEFT JOIN fields rf ON rf.id = f.reference_field_id
          LEFT JOIN fields rvf ON rvf.id = f.reference_value_field_id
        WHERE (rb.id IS NULL AND f.reference_business_id IS NOT NULL)
           OR (rf.id IS NULL AND f.reference_field_id IS NOT NULL)
           OR (rvf.id IS NULL AND f.reference_value_field_id IS NOT NULL)
      SQL

      orphaned_records = ActiveRecord::Base.connection
        .exec_query(orphaned_fields_sql)
        .map { |row| "'#{row['id']}'" }

      if orphaned_records.any?
        # Single SQL update to nullify orphaned references and soft delete
        update_sql = <<~SQL.squish
          UPDATE fields
          SET reference_business_id = NULL,
              reference_field_id = NULL,
              reference_value_field_id = NULL,
              deleted_at = COALESCE(deleted_at, NOW())
          WHERE id IN (#{orphaned_records.join(', ')})
        SQL

        ActiveRecord::Base.transaction do
          updated_count = ActiveRecord::Base.connection.execute(update_sql).cmd_tuples
          total_updated += updated_count
          puts "  Updated and soft-deleted #{updated_count} fields in #{company.subdomain}"
        end
      else
        puts "  No orphaned references found in #{company.subdomain}"
      end
    end

    elapsed_time = Time.current - start_time
    puts "\nCleanup completed!"
    puts "Total updated and soft-deleted: #{total_updated} fields in #{elapsed_time.round(2)}s"
  end
end
