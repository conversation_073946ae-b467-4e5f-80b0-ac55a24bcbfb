{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"type": "text", "version": 294, "versionNonce": 608423343, "isDeleted": false, "id": "ZCZV2jJcKAGyQvNpXptqX", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 442.2239731126152, "y": -507.4768037391889, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 196.43896484375, "height": 36.00000000000002, "seed": 1606147536, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1731440121953, "link": null, "locked": false, "fontSize": 28.800000000000015, "fontFamily": 5, "text": "Logica de regra", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Logica de regra", "lineHeight": 1.25, "baseline": 29}, {"type": "text", "version": 218, "versionNonce": 216819905, "isDeleted": false, "id": "-0MhqRXzHva_vIbmTfBa6", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 231.75724998842907, "y": -439.3101370725233, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 587.65625, "height": 25, "seed": 727590704, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1731440121955, "link": null, "locked": false, "fontSize": 20, "fontFamily": 5, "text": "Modificar apenas o formato que o serviço roda determinada regra.", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Modificar apenas o formato que o serviço roda determinada regra.", "lineHeight": 1.25, "baseline": 21}, {"type": "text", "version": 39, "versionNonce": 1425817551, "isDeleted": false, "id": "i1Rv8EEc8f7B19IgRFcUw", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -19.437203339405073, "y": -370.31013707252225, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 991.9921875, "height": 2250, "seed": 486964748, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1731440121957, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "{\n    \"rule_actions\": [\n        {\n            \"field_disabled\": false,\n            \"field_hidden\": false,\n            \"field_required\": true,\n            \"field_visible\": false,\n            \"value\": \"321\",\n            \"field\": \"d9f21f0a-0e2e-443f-9181-59daba5cc994:de9ff70f-2b67-4173-9ce5-c0a96f528767\"\n        }\n    ],\n    \"rules\": {\n        \"rules\": [\n            {\n                \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:6f689139-871f-4a79-9519-84271d27f1c4\",\n                \"value\": \"<PERSON>and<PERSON>\",\n                \"operator\": \"equals\",\n                \"overview_index\": \"A\"\n            },\n            {\n                \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:6129c921-bc56-4a1e-9702-c82fade6bbaf\",\n                \"value\": \"Teste\",\n                \"operator\": \"equals\",\n                \"overview_index\": \"B\"\n            },\n            {\n                \"rules\": [\n                    {\n                        \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:59e40b5c-b932-43fc-979d-545381536285\",\n                        \"value\": \"7\",\n                        \"operator\": \"equals\",\n                        \"overview_index\": \"C\"\n                    },\n                    {\n                        \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:ba30f11a-783c-44ed-b8e0-d0b00b4f2b4d\",\n                        \"value\": \"4\",\n                        \"operator\": \"equals\",\n                        \"overview_index\": \"D\"\n                    },\n                    {\n                        \"rules\": [\n                            {\n                                \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:87a59625-2147-46e3-a7e1-eb8cc877ccfa\",\n                                \"value\": \"123\",\n                                \"operator\": \"equals\",\n                                \"overview_index\": \"E\"\n                            },\n                            {\n                                \"field\": \"d9f21f0a-0e2e-443f-9181-59daba5cc994:27d5da93-2419-46a7-897f-fe19f0dcf70b\",\n                                \"value\": \"dois\",\n                                \"operator\": \"not_equal\",\n                                \"overview_index\": \"F\"\n                            }\n                        ],\n                        \"condition\": \"or\"\n                    },\n                    {\n                        \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:7a7d361f-33da-4228-8003-0ca804abe998\",\n                        \"value\": [\n                            \"wgeah\"\n                        ],\n                        \"operator\": \"equals\",\n                        \"overview_index\": \"G\"\n                    }\n                ],\n                \"condition\": \"and\"\n            },\n            {\n                \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:248bd4ee-de9b-4b41-a9ee-5924ef651814\",\n                \"value\": \"afe\",\n                \"operator\": \"not_equal\",\n                \"overview_index\": \"H\"\n            }\n        ],\n        \"overview\": \"(A && B && (C && D && (E || F) && G) && H)\",\n        \"condition\": \"and\"\n    },\n    \"description\": \"\",\n    \"id\": \"eb4d6664-7efb-4478-bcc2-c330256a5443\",\n    \"rule_type\": \"dependent\",\n    \"business_id\": \"51cf87b0-9e59-41e8-bfa7-c0c9e4d9f8f3\",\n    \"field_disabled\": false,\n    \"field_disabled_humanize\": \"Não\",\n    \"field_hidden\": false,\n    \"field_hidden_humanize\": \"Não\",\n    \"field_required\": false,\n    \"field_required_humanize\": \"Não\",\n    \"field_visible\": false,\n    \"field_visible_humanize\": \"Não\"\n}", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "{\n    \"rule_actions\": [\n        {\n            \"field_disabled\": false,\n            \"field_hidden\": false,\n            \"field_required\": true,\n            \"field_visible\": false,\n            \"value\": \"321\",\n            \"field\": \"d9f21f0a-0e2e-443f-9181-59daba5cc994:de9ff70f-2b67-4173-9ce5-c0a96f528767\"\n        }\n    ],\n    \"rules\": {\n        \"rules\": [\n            {\n                \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:6f689139-871f-4a79-9519-84271d27f1c4\",\n                \"value\": \"<PERSON>and<PERSON>\",\n                \"operator\": \"equals\",\n                \"overview_index\": \"A\"\n            },\n            {\n                \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:6129c921-bc56-4a1e-9702-c82fade6bbaf\",\n                \"value\": \"Teste\",\n                \"operator\": \"equals\",\n                \"overview_index\": \"B\"\n            },\n            {\n                \"rules\": [\n                    {\n                        \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:59e40b5c-b932-43fc-979d-545381536285\",\n                        \"value\": \"7\",\n                        \"operator\": \"equals\",\n                        \"overview_index\": \"C\"\n                    },\n                    {\n                        \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:ba30f11a-783c-44ed-b8e0-d0b00b4f2b4d\",\n                        \"value\": \"4\",\n                        \"operator\": \"equals\",\n                        \"overview_index\": \"D\"\n                    },\n                    {\n                        \"rules\": [\n                            {\n                                \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:87a59625-2147-46e3-a7e1-eb8cc877ccfa\",\n                                \"value\": \"123\",\n                                \"operator\": \"equals\",\n                                \"overview_index\": \"E\"\n                            },\n                            {\n                                \"field\": \"d9f21f0a-0e2e-443f-9181-59daba5cc994:27d5da93-2419-46a7-897f-fe19f0dcf70b\",\n                                \"value\": \"dois\",\n                                \"operator\": \"not_equal\",\n                                \"overview_index\": \"F\"\n                            }\n                        ],\n                        \"condition\": \"or\"\n                    },\n                    {\n                        \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:7a7d361f-33da-4228-8003-0ca804abe998\",\n                        \"value\": [\n                            \"wgeah\"\n                        ],\n                        \"operator\": \"equals\",\n                        \"overview_index\": \"G\"\n                    }\n                ],\n                \"condition\": \"and\"\n            },\n            {\n                \"field\": \"e64ffea2-9fd7-4eb9-b149-065cd0adba4a:248bd4ee-de9b-4b41-a9ee-5924ef651814\",\n                \"value\": \"afe\",\n                \"operator\": \"not_equal\",\n                \"overview_index\": \"H\"\n            }\n        ],\n        \"overview\": \"(A && B && (C && D && (E || F) && G) && H)\",\n        \"condition\": \"and\"\n    },\n    \"description\": \"\",\n    \"id\": \"eb4d6664-7efb-4478-bcc2-c330256a5443\",\n    \"rule_type\": \"dependent\",\n    \"business_id\": \"51cf87b0-9e59-41e8-bfa7-c0c9e4d9f8f3\",\n    \"field_disabled\": false,\n    \"field_disabled_humanize\": \"Não\",\n    \"field_hidden\": false,\n    \"field_hidden_humanize\": \"Não\",\n    \"field_required\": false,\n    \"field_required_humanize\": \"Não\",\n    \"field_visible\": false,\n    \"field_visible_humanize\": \"Não\"\n}", "lineHeight": 1.25, "baseline": 2246}, {"type": "line", "version": 119, "versionNonce": 459041072, "isDeleted": false, "id": "K_70U2lCNMGK1_4Xu9X1z", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 933.9964193001548, "y": 1454.6920757242442, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 610, "height": 3.333333333333485, "seed": 1007726384, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1731438537986, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [-610, 3.333333333333485]]}, {"type": "arrow", "version": 519, "versionNonce": 259267888, "isDeleted": false, "id": "MnRWD3U7gAGHACZyFFaph", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 35.090583321764825, "y": 1619.0231962608082, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 422.1038091251548, "height": 76.00000000000045, "seed": 902541616, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1731433036478, "link": null, "locked": false, "startBinding": null, "endBinding": {"elementId": "eRdZv9j9bkkGlnuqRIUTs", "focus": -0.0714948932219056, "gap": 10.666666666666686, "fixedPoint": null}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-422.1038091251548, 76.00000000000045]]}, {"type": "text", "version": 521, "versionNonce": 935569569, "isDeleted": false, "id": "eRdZv9j9bkkGlnuqRIUTs", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -1061.5760833449021, "y": 1705.6898629274754, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 640.634765625, "height": 100, "seed": 263481808, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "MnRWD3U7gAGHACZyFFaph", "type": "arrow"}], "updated": 1731440121958, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Rule Type\n\n- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (obs: Eram separadas mas foram (unificadas))\n- Validação", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Rule Type\n\n- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (obs: Eram separadas mas foram (unificadas))\n- Validação", "lineHeight": 1.25, "baseline": 96}, {"type": "text", "version": 152, "versionNonce": 1445031407, "isDeleted": false, "id": "DbyxAJggBEngYwjEMcAQS", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -788.2427500115684, "y": -62.643470405857386, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 89.7265625, "height": 25, "seed": 763947824, "groupIds": [], "frameId": "hVHNSFFOa8_sg_9ErSB_U", "roundness": null, "boundElements": [], "updated": 1731440121958, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Conjuntos", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Conjuntos", "lineHeight": 1.25, "baseline": 21}, {"type": "text", "version": 231, "versionNonce": 694676609, "isDeleted": false, "id": "C8OLFyzLKgsDJM8EEgrVH", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -1044.9094166782352, "y": 22.356529594142614, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 462.01171875, "height": 50, "seed": 833869776, "groupIds": [], "frameId": "hVHNSFFOa8_sg_9ErSB_U", "roundness": null, "boundElements": [], "updated": 1731440121958, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Conjunto é uma lista de regras que podem ter varias\nregras e/ou vários outros conjuntos.", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Conjunto é uma lista de regras que podem ter varias regras e/ou vários outros conjuntos.", "lineHeight": 1.25, "baseline": 46}, {"type": "text", "version": 220, "versionNonce": 1924913167, "isDeleted": false, "id": "fSFS1kpCVgexUiHJm0S75", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -1044.9094166782347, "y": 87.35652959414267, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 524.833984375, "height": 75, "seed": 1060051760, "groupIds": [], "frameId": "hVHNSFFOa8_sg_9ErSB_U", "roundness": null, "boundElements": [], "updated": 1731440121959, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Cada conjunto tem sua condição que pode ser \"OR\" ou\n\"AND\", regras não tem condições pois sempre estão dentro\nde algum conjunto.", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Cada conjunto tem sua condição que pode ser \"OR\" ou \"AND\", regras não tem condições pois sempre estão dentro de algum conjunto.", "lineHeight": 1.25, "baseline": 71}, {"type": "text", "version": 47, "versionNonce": 161002593, "isDeleted": false, "id": "t-wJKg8CimYmvVPBvEn_L", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -769.9094166782343, "y": 199.02319626080936, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 50.771484375, "height": 25, "seed": 29637072, "groupIds": [], "frameId": "hVHNSFFOa8_sg_9ErSB_U", "roundness": null, "boundElements": [], "updated": 1731440121959, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Regra", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Regra", "lineHeight": 1.25, "baseline": 21}, {"type": "text", "version": 250, "versionNonce": 12844591, "isDeleted": false, "id": "Sb3WEU7MmNXzE2LOOqEfd", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -1049.9094166782343, "y": 252.35652959414284, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 490.986328125, "height": 75, "seed": 147785008, "groupIds": [], "frameId": "hVHNSFFOa8_sg_9ErSB_U", "roundness": null, "boundElements": [], "updated": 1731440121959, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Cada regra tem as informações que vão ser usadas para\nvalidar as condições especificadas no conjunto. Como o\ncampo, valor e operador.", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Cada regra tem as informações que vão ser usadas para validar as condições especificadas no conjunto. Como o campo, valor e operador.", "lineHeight": 1.25, "baseline": 71}, {"type": "frame", "version": 66, "versionNonce": 345678800, "isDeleted": false, "id": "hVHNSFFOa8_sg_9ErSB_U", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": -1106.5760833449015, "y": -99.31013707252401, "strokeColor": "#bbb", "backgroundColor": "transparent", "width": 748.3333333333333, "height": 494.9999999999999, "seed": 819753936, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "QkYCJed0ku9ilVsqBM-2L", "type": "arrow"}], "updated": 1731433471432, "link": null, "locked": false, "name": null}, {"type": "arrow", "version": 252, "versionNonce": 2066296624, "isDeleted": false, "id": "QkYCJed0ku9ilVsqBM-2L", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 15.423916655095809, "y": -79.65957346543797, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 366.666666666664, "height": 101.47049265335995, "seed": 1015943472, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1731433475058, "link": null, "locked": false, "startBinding": null, "endBinding": {"elementId": "hVHNSFFOa8_sg_9ErSB_U", "focus": -0.05952425840464828, "gap": 7, "fixedPoint": null}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-366.666666666664, 101.47049265335995]]}, {"type": "text", "version": 106, "versionNonce": 1545961537, "isDeleted": false, "id": "VVTnE_wttMRev4fjrz3It", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -921.254654773476, "y": 412.20176768938245, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 354.23828125, "height": 135, "seed": 647461684, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1731440121960, "link": null, "locked": false, "fontSize": 20, "fontFamily": 6, "text": "\"rules\": {\n\"rules\": [{}, {}, {\n\"rules\": [{}, {}, {\n\"rules\": [{}, {}], \"condition\": \"X\"}, \n{}], \"condition\": \"X\" }, {}], \"condition\": \"X\"}", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "\"rules\": {\n\"rules\": [{}, {}, {\n\"rules\": [{}, {}, {\n\"rules\": [{}, {}], \"condition\": \"X\"}, \n{}], \"condition\": \"X\" }, {}], \"condition\": \"X\"}", "lineHeight": 1.35, "baseline": 130}, {"type": "text", "version": 157, "versionNonce": 52584527, "isDeleted": false, "id": "wAEe8DeR_U-f_TzpltT4W", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 2004.2286568465415, "y": -33.068115156783534, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 204.208984375, "height": 25, "seed": 428824368, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121960, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "BusinessesRulesRunner", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "BusinessesRulesRunner", "lineHeight": 1.25, "baseline": 21}, {"type": "text", "version": 1098, "versionNonce": 2129514529, "isDeleted": false, "id": "cBO4l3TtkaN3RjpHs_kOA", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1662.317755467367, "y": 104.12935744854275, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 794.66796875, "height": 375.00000000000045, "seed": 100462896, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121961, "link": null, "locked": false, "fontSize": 20.000000000000025, "fontFamily": 8, "text": "Vai receber uma answer no initialize da classe e vai ter um método pai por exemplo \"run\".\n\n\nObjetivo do método run: Modificar a answer que a classe que ele pertence recebeu.\n\n\nLogica per se: \n    - criar um hash com valor de todas as answers apartir do content da answer passado no\ninitialize\n    - Filtrar as regras por id do business (Obs: Business vai ser buscado pelo content)\n    - Filtrar as regras por step_id usando o step_id da answer\n    - Vai iterar sobre regras e chamar rule_match? para cada uma delas\n    - - Se der match ele vai pegar todas as rules_actions daquela rule e aplicar\n    - - Caso não der match continua\n", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Vai receber uma answer no initialize da classe e vai ter um método pai por exemplo \"run\".\n\n\nObjetivo do método run: Modificar a answer que a classe que ele pertence recebeu.\n\n\nLogica per se: \n    - criar um hash com valor de todas as answers apartir do content da answer passado no initialize\n    - Filtrar as regras por id do business (Obs: Business vai ser buscado pelo content)\n    - Filtrar as regras por step_id usando o step_id da answer\n    - Vai iterar sobre regras e chamar rule_match? para cada uma delas\n    - - Se der match ele vai pegar todas as rules_actions daquela rule e aplicar\n    - - Caso não der match continua\n", "lineHeight": 1.25, "baseline": 371}, {"type": "text", "version": 278, "versionNonce": 196050543, "isDeleted": false, "id": "Vkic5BdfXHT8QvDm6QHWQ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 2000.3326052375048, "y": 934.0557484051013, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 170.263671875, "height": 25, "seed": 196107216, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121961, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Private rule_match?", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Private rule_match?", "lineHeight": 1.25, "baseline": 21}, {"type": "text", "version": 524, "versionNonce": 1742808065, "isDeleted": false, "id": "VzDmlIOAXxw32vCJtZvac", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1662.9547225937595, "y": 40.68986292747749, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 763.88671875, "height": 50, "seed": 1904286160, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121962, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Variáveis de escopo de classe: @content_values, @matched_actions: { \"step_id:field_id\":\nactions}", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Variáveis de escopo de classe: @content_values, @matched_actions: { \"step_id:field_id\": actions}", "lineHeight": 1.25, "baseline": 46}, {"type": "text", "version": 242, "versionNonce": 1563678863, "isDeleted": false, "id": "cjdreNGXvkz9rxbO-HEX-", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1939.7536857539485, "y": 1281.2059115360805, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 262.75390625, "height": 25, "seed": 690403675, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121963, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Private conditions_set_match?", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Private conditions_set_match?", "lineHeight": 1.25, "baseline": 21}, {"type": "text", "version": 725, "versionNonce": 587135969, "isDeleted": false, "id": "MtYsm7Nqi9MyWQmicDT5H", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1648.7536857539485, "y": 1354.2059115360805, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 874.8046875, "height": 325, "seed": 696813525, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121965, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Vai receber uma condition_set (conjunto de condições)\n\nObjetivo do método: Vai retornar true se todos as condições dentro do conjunto de condições der\nmatch.\n\nLógica per se:\n    - Iterar sobre o array de condições recebido\n        - Caso o objeto sendo iterado for uma condição única, vai chamar condition_match?\n        - Caso o objeto sendo iterado for uma condition_set, chamar recusivamente o próprio método\nconditions_set_match?\n        - Guarda todos os resultados booleanos num array (conditions_result)\n    - Utilizar o operador \"and\" ou \"or\" presente no campo condition da condition_set recebida, \npara resolver os valores do array conditions_result", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Vai receber uma condition_set (conjunto de condições)\n\nObjetivo do método: Vai retornar true se todos as condições dentro do conjunto de condições der\nmatch.\n\nLógica per se:\n    - Iterar sobre o array de condições recebido\n        - Caso o objeto sendo iterado for uma condição única, vai chamar condition_match?\n        - Caso o objeto sendo iterado for uma condition_set, chamar recusivamente o próprio método\nconditions_set_match?\n        - Guarda todos os resultados booleanos num array (conditions_result)\n    - Utilizar o operador \"and\" ou \"or\" presente no campo condition da condition_set recebida, \npara resolver os valores do array conditions_result", "lineHeight": 1.25, "baseline": 321}, {"type": "text", "version": 110, "versionNonce": 1298656943, "isDeleted": false, "id": "wpJPwH1x2w0klMXr3qTUS", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1987.7536857539485, "y": 1786.2059115360805, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 220.244140625, "height": 25, "seed": 237497877, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121965, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Private condition_match?", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Private condition_match?", "lineHeight": 1.25, "baseline": 21}, {"type": "text", "version": 422, "versionNonce": 274126785, "isDeleted": false, "id": "34rtvWFN9cz0eA9HBbO_T", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1649.7536857539485, "y": 1844.2059115360805, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 805.849609375, "height": 200, "seed": 505560731, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121966, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Vai receber uma condition (condição única)\n\nObjetivo do método: Retorna true se a condição der match\n\nLógica per se:\n    - Busca o valor do campo (field) dentro do content_values\n    - Utiliza o operador definido na condição, para comparar o valor do field (content_value) \ncom o campo value da condição", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Vai receber uma condition (condição única)\n\nObjetivo do método: Retorna true se a condição der match\n\nLógica per se:\n    - Busca o valor do campo (field) dentro do content_values\n    - Utiliza o operador definido na condição, para comparar o valor do field (content_value) \ncom o campo value da condição", "lineHeight": 1.25, "baseline": 196}, {"type": "frame", "version": 257, "versionNonce": 247694133, "isDeleted": false, "id": "nVxcml0hJrrpMk8b8p8-P", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1560.7572499884327, "y": -74.3101370725226, "strokeColor": "#bbb", "backgroundColor": "transparent", "width": 1173.3333333333335, "height": 2759.000000000001, "seed": 1725252400, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1731439606739, "link": null, "locked": false, "name": null}, {"type": "text", "version": 73, "versionNonce": 1727283407, "isDeleted": false, "id": "GyepU9yVp2Hlh3apAk7yy", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 335.0310595122386, "y": -2163.4202957531347, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 277.56451416015625, "height": 90.90777772823367, "seed": 1894690868, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1731440121968, "link": null, "locked": false, "fontSize": 72.72622218258694, "fontFamily": 5, "text": "Cenários", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Cenários", "lineHeight": 1.25, "baseline": 75}, {"type": "text", "version": 164, "versionNonce": 1644705697, "isDeleted": false, "id": "yl4C8CMopEo_o7B--7fMt", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 259.3167737979528, "y": -2024.655375167758, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 723.076171875, "height": 75, "seed": 1608840076, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1731440121969, "link": null, "locked": false, "fontSize": 20, "fontFamily": 5, "text": "1º - Regra em um Campo/Template que existe em mais de uma etapa do negócio;\n\n2º - Campos Subnegócio precisa receber \"true\" no seu ", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "1º - Regra em um Campo/Template que existe em mais de uma etapa do negócio;\n\n2º - Campos Subnegócio precisa receber \"true\" no seu ", "lineHeight": 1.25, "baseline": 71}, {"type": "text", "version": 1378, "versionNonce": 1265716975, "isDeleted": false, "id": "xCgApRLr_aKvAPGqXXmfF", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1638.6659385708376, "y": 987.3890817384346, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 776.69921875, "height": 175, "seed": 1474107344, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121969, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Vai receber uma rule\n\nObjetivo do método rule_match?: Vai retornar true se todas as condições da rule que ele\nrecebeu derem match.\n\nLogica per se:\n    - <PERSON><PERSON> conditions_set_match?", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Vai receber uma rule\n\nObjetivo do método rule_match?: Vai retornar true se todas as condições da rule que ele recebeu derem match.\n\nLogica per se:\n    - <PERSON><PERSON> conditions_set_match?", "lineHeight": 1.25, "baseline": 171}, {"type": "text", "version": 299, "versionNonce": 1514314625, "isDeleted": false, "id": "T_aDjSXMurGj7QFe55z-Z", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0.0027397191725633263, "x": 2042.4239726426567, "y": 552.3156586002741, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 113.1640625, "height": 25, "seed": 1643645744, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121969, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Private apply", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Private apply", "lineHeight": 1.25, "baseline": 21}, {"type": "text", "version": 1974, "versionNonce": 1432852751, "isDeleted": false, "id": "-66vbUbqA6pcJ56glcDwi", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1633.2732061569536, "y": 616.0083464906733, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 798.57421875, "height": 250, "seed": 702257104, "groupIds": [], "frameId": "nVxcml0hJrrpMk8b8p8-P", "roundness": null, "boundElements": [], "updated": 1731440121970, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Vai receber rule\n\nObjetivo do método: Atualizar @content_values e @matched_actions\n\nLogica per se:\n    - pegar todos os rule_actions dessa rule\n    - iterar as rule_actions \n    - - para cada rule_actions criar um registro no @matched_actions com os valores da rule\nactions e atualizar o @content_values com o valor caso tenha.\n    - retorna nada", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Vai receber rule\n\nObjetivo do método: Atualizar @content_values e @matched_actions\n\nLogica per se:\n    - pegar todos os rule_actions dessa rule\n    - iterar as rule_actions \n    - - para cada rule_actions criar um registro no @matched_actions com os valores da rule actions e atualizar o @content_values com o valor caso tenha.\n    - retorna nada", "lineHeight": 1.25, "baseline": 246}, {"type": "text", "version": 88, "versionNonce": 1229292385, "isDeleted": false, "id": "Sua1vKjNnyXVncSM7tbsj", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1435.7812973778648, "y": -920.4273610236512, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 388.115234375, "height": 25, "seed": 80804304, "groupIds": [], "frameId": "OKaGhNjaSkMNFQchEpbbK", "roundness": null, "boundElements": [], "updated": 1731440121970, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "Como aumentar o limit da vm stack do ruby", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Como aumentar o limit da vm stack do ruby", "lineHeight": 1.25, "baseline": 21}, {"type": "text", "version": 77, "versionNonce": 2052038447, "isDeleted": false, "id": "TSBOHRh8GMmRvebdqQNFF", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1412.3737151938435, "y": -817.4339994139586, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 437.7734375, "height": 75, "seed": 1178246096, "groupIds": [], "frameId": "OKaGhNjaSkMNFQchEpbbK", "roundness": null, "boundElements": [], "updated": 1731440121970, "link": null, "locked": false, "fontSize": 20, "fontFamily": 8, "text": "https://stackoverflow.com/questions/242617/how\n-to-increase-stack-size-for-a-ruby-app-\nrecursive-app-getting-stack-level-to", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "https://stackoverflow.com/questions/242617/how-to-increase-stack-size-for-a-ruby-app-recursive-app-getting-stack-level-to", "lineHeight": 1.25, "baseline": 71}, {"type": "frame", "version": 26, "versionNonce": 2127782704, "isDeleted": false, "id": "OKaGhNjaSkMNFQchEpbbK", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1310.9408590630856, "y": -987.529096617845, "strokeColor": "#bbb", "backgroundColor": "transparent", "width": 705.3484764785012, "height": 486.8777094276379, "seed": 755388720, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1731439016798, "link": null, "locked": false, "name": null}], "appState": {"gridSize": 20, "viewBackgroundColor": "#ffffff"}, "files": {}}