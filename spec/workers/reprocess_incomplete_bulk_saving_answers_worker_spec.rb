require 'rails_helper'

RSpec.describe ReprocessIncompleteBulkSavingAnswersWorker, type: :worker do
  describe 'perform' do
    subject { described_class.new.perform(Apartment::Tenant.current) }

    let(:service) { double(:service, process_orphans: true) }

    before { allow(BulkAnswerService).to receive(:new).and_return(service) }

    context 'when there is no created or processing bulk saving answer' do
      it 'does nothing' do
        expect(BulkAnswerService).not_to receive(:new)
        expect(service).not_to receive(:process_orphans)

        subject
      end
    end

    context 'when there is a created bulk saving answer' do
      context 'and its status is done' do
        let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies, status: :done, created_at: 16.hours.ago) }

        before do
          create(:answer_processing, status: :failed, bulk_saving_answer: bulk_saving_answer)
          create(:answer_processing, status: :done, bulk_saving_answer: bulk_saving_answer)
        end

        it 'does nothing' do
          expect(BulkAnswerService).not_to receive(:new)
          expect(service).not_to receive(:process_orphans)

          subject
        end
      end

      %i[created processing].each do |status|
        let(:bulk_saving_answer) { create(:bulk_saving_answer, :with_step_dependencies, status: status, created_at: 16.hours.ago) }

        context "and its status is #{status}" do
          context 'and it does not have all answers processing done or failed' do
            before do
              create(:answer_processing, status: :failed, bulk_saving_answer: bulk_saving_answer)
              create(:answer_processing, status: :done, bulk_saving_answer: bulk_saving_answer)
              create(:answer_processing, status: :processing, bulk_saving_answer: bulk_saving_answer)
              create(:answer_processing, status: :created, bulk_saving_answer: bulk_saving_answer)
            end

            it 'processes the bulk saving answer orphans' do
              expect(BulkAnswerService).to receive(:new)
              expect(service).to receive(:process_orphans)

              subject
            end
          end

          context 'and it has all answers processing done or failed' do
            let(:user) { create(:user, email: ReprocessIncompleteBulkSavingAnswersWorker::REPROCESS_USER_EMAIL) }

            before do
              create(:answer_processing, status: :failed, bulk_saving_answer: bulk_saving_answer)
              create(:answer_processing, status: :done, bulk_saving_answer: bulk_saving_answer)
            end

            it 'does nothing' do
              expect(BulkAnswerService).not_to receive(:new)
              expect(service).not_to receive(:process_orphans)

              subject
            end
          end
        end
      end
    end
  end
end
