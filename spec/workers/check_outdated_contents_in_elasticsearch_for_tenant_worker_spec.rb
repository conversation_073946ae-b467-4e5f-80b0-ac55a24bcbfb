require 'rails_helper'

RSpec.describe CheckOutdatedContentsInElasticsearchForTenantWorker, type: :worker do
  include ActiveSupport::Testing::TimeHelpers
  let(:tenant) { 'test_tenant' }
  let(:company) { create(:company, subdomain: tenant, use_elasticsearch: true) }
  let(:business) { create(:business, :with_dependencies, integrate_elastic: true) }
  let(:elasticsearch_service) { double(:elasticsearch_service) }
  let(:elasticsearch_client) { double(:elasticsearch_client) }
  let(:elasticsearch_indices) { double(:elasticsearch_indices) }

  before do
    allow(ElasticSearcherService).to receive(:new).and_return(elasticsearch_service)
    allow(elasticsearch_service).to receive(:client).and_return(elasticsearch_client)
    allow(elasticsearch_client).to receive(:indices).and_return(elasticsearch_indices)
    allow(Apartment::Tenant).to receive(:switch!).with(tenant)
    allow(Apartment::Tenant).to receive(:current).and_return(tenant)
  end

  describe '#perform' do
    subject { described_class.new.perform(tenant, last_checked_elasticsearch) }

    context 'when last_checked_elasticsearch is nil (first run)' do
      let(:last_checked_elasticsearch) { nil }

      before do
        allow(Company).to receive(:find_by).with(subdomain: tenant).and_return(company)
        allow(Business).to receive_message_chain(:kept, :where, :find_each).and_yield(business)
        allow(elasticsearch_indices).to receive(:exists?).and_return(true)
        allow(Content).to receive_message_chain(:unscoped, :where, :find_in_batches)
      end

      it 'switches to the correct tenant' do
        expect(Apartment::Tenant).to receive(:switch!).with(tenant)
        subject
      end

      it 'updates the company last_checked_elasticsearch timestamp' do
        freeze_time do
          expect(company).to receive(:update!).with(last_checked_elasticsearch: Time.now.iso8601)
          subject
        end
      end

      it 'processes businesses with integrate_elastic enabled' do
        expect(Business).to receive_message_chain(:kept, :where).with(integrate_elastic: true)
        subject
      end
    end

    context 'when last_checked_elasticsearch has a value (subsequent runs)' do
      let(:last_checked_elasticsearch) { 1.hour.ago }
      let(:content) { create(:content, business: business, updated_at: 30.minutes.ago) }

      before do
        allow(Company).to receive(:find_by).with(subdomain: tenant).and_return(company)
        allow(Business).to receive_message_chain(:kept, :where, :find_each).and_yield(business)
        allow(elasticsearch_indices).to receive(:exists?).and_return(true)

        # Mock the content query chain
        contents_relation = double(:contents_relation)
        allow(Content).to receive(:unscoped).and_return(contents_relation)
        allow(contents_relation).to receive(:where).with(business: business, draft: false).and_return(contents_relation)
        allow(contents_relation).to receive(:where).with(updated_at: last_checked_elasticsearch).and_return(contents_relation)
        allow(contents_relation).to receive(:find_in_batches).with(batch_size: 4000)
      end

      it 'filters contents by last_checked_elasticsearch timestamp' do
        contents_relation = double(:contents_relation)
        allow(Content).to receive(:unscoped).and_return(contents_relation)
        allow(contents_relation).to receive(:where).with(business: business, draft: false).and_return(contents_relation)

        expect(contents_relation).to receive(:where).with(updated_at: last_checked_elasticsearch).and_return(contents_relation)
        allow(contents_relation).to receive(:find_in_batches).with(batch_size: 4000)

        subject
      end
    end
  end

  describe '#process_businesses' do
    let(:worker) { described_class.new }
    let(:last_checked_elasticsearch) { nil }

    context 'when business has no elasticsearch index' do
      before do
        allow(Business).to receive_message_chain(:kept, :where, :find_each).and_yield(business)
        allow(elasticsearch_indices).to receive(:exists?).with(index: "#{tenant}-#{business.id}").and_return(false)
      end

      it 'enqueues BusinessSetupWorker for index creation' do
        expect(Elasticsearch::BusinessSetupWorker).to receive(:perform_async).with(tenant, business.id)
        worker.send(:process_businesses, tenant, last_checked_elasticsearch)
      end

      it 'skips content processing for that business' do
        allow(Elasticsearch::BusinessSetupWorker).to receive(:perform_async)
        expect(Content).not_to receive(:unscoped)
        worker.send(:process_businesses, tenant, last_checked_elasticsearch)
      end
    end

    context 'when business has elasticsearch index' do
      let(:content1) { double(:content, id: 1, updated_at: 1.hour.ago) }
      let(:content2) { double(:content, id: 2, updated_at: 30.minutes.ago) }
      let(:contents) { [content1, content2] }

      before do
        allow(Business).to receive_message_chain(:kept, :where, :find_each).and_yield(business)
        allow(elasticsearch_indices).to receive(:exists?).with(index: "#{tenant}-#{business.id}").and_return(true)

        # Mock the content query chain
        contents_relation = double(:contents_relation)
        allow(Content).to receive(:unscoped).and_return(contents_relation)
        allow(contents_relation).to receive(:where).with(business: business, draft: false).and_return(contents_relation)
        allow(contents_relation).to receive(:find_in_batches).with(batch_size: 4000).and_yield(contents)

        allow(worker).to receive(:search_outdated_contents)
      end

      it 'processes contents in batches' do
        expect(worker).to receive(:search_outdated_contents).with(business, contents)
        worker.send(:process_businesses, tenant, last_checked_elasticsearch)
      end
    end
  end

  describe '#search_outdated_contents' do
    let(:worker) { described_class.new }
    let(:content1) { double(:content, id: 1, updated_at: 1.hour.ago) }
    let(:content2) { double(:content, id: 2, updated_at: 30.minutes.ago) }
    let(:contents) { [content1, content2] }

    before do
      allow(contents).to receive(:pluck).with(:id).and_return([1, 2])
      allow(contents).to receive(:pluck).with(:id, :updated_at).and_return([[1, content1.updated_at], [2, content2.updated_at]])
    end

    context 'when elasticsearch search returns results' do
      let(:elasticsearch_response) do
        {
          'hits' => {
            'hits' => [
              {
                'fields' => {
                  '__CONTENT_ID' => [1],
                  '__ATUALIZADO_EM' => [1.hour.ago.iso8601]
                }
              }
            ]
          }
        }
      end

      before do
        allow(worker).to receive(:search_in_elasticsearch).and_return(elasticsearch_response)
        # Set up content timestamps to match what's expected in the test
        time_1_hour_ago = 1.hour.ago
        allow(content1).to receive(:updated_at).and_return(time_1_hour_ago)
        allow(content2).to receive(:updated_at).and_return(30.minutes.ago)

        # Update the elasticsearch response to use the same timestamp format as the worker expects
        elasticsearch_response['hits']['hits'][0]['fields']['__ATUALIZADO_EM'] = [time_1_hour_ago.utc.strftime("%Y-%m-%dT%H:%M:%S.000Z")]
      end

      it 'identifies missing contents and enqueues them for reindexing' do
        # Content 1 is found in elasticsearch with matching timestamp, content 2 is missing
        expect(worker).to receive(:enqueue_missing_contents).with(tenant, [2])
        worker.send(:search_outdated_contents, business, contents)
      end

      it 'identifies outdated contents and enqueues them for reindexing' do
        # Simulate content being outdated in database vs elasticsearch
        updated_time = 30.minutes.ago
        allow(content1).to receive(:updated_at).and_return(updated_time) # More recent than elasticsearch

        # Update the pluck mock to return the new timestamp
        allow(contents).to receive(:pluck).with(:id, :updated_at).and_return([[1, updated_time], [2, content2.updated_at]])

        expect(worker).to receive(:enqueue_missing_contents).with(tenant, [1, 2])
        worker.send(:search_outdated_contents, business, contents)
      end
    end

    context 'when elasticsearch search returns no response' do
      before do
        allow(worker).to receive(:search_in_elasticsearch).and_return(nil)
      end

      it 'does not enqueue any contents' do
        expect(worker).not_to receive(:enqueue_missing_contents)
        worker.send(:search_outdated_contents, business, contents)
      end
    end
  end

  describe '#enqueue_missing_contents' do
    let(:worker) { described_class.new }
    let(:content_ids) { [1, 2, 3] }

    it 'enqueues PutDocumentWorker jobs in bulk' do
      expected_args = content_ids.map { |id| [tenant, id] }
      expect(Elasticsearch::PutDocumentWorker).to receive(:perform_bulk).with(expected_args)

      worker.send(:enqueue_missing_contents, tenant, content_ids)
    end
  end

  describe '#index_exists?' do
    let(:worker) { described_class.new }
    let(:index_name) { "#{tenant}-#{business.id}" }

    context 'when index exists' do
      before do
        allow(elasticsearch_indices).to receive(:exists?).with(index: index_name).and_return(true)
      end

      it 'returns true' do
        expect(worker.send(:index_exists?, index_name)).to be true
      end
    end

    context 'when index does not exist' do
      before do
        allow(elasticsearch_indices).to receive(:exists?).with(index: index_name).and_return(false)
      end

      it 'returns false' do
        expect(worker.send(:index_exists?, index_name)).to be false
      end
    end

    context 'when elasticsearch raises an error' do
      before do
        allow(elasticsearch_indices).to receive(:exists?).with(index: index_name).and_raise(StandardError.new('Connection error'))
      end

      it 'returns false' do
        expect(worker.send(:index_exists?, index_name)).to be false
      end
    end
  end

  describe '#search_in_elasticsearch' do
    let(:worker) { described_class.new }
    let(:content_ids) { [1, 2, 3] }
    let(:index_name) { "#{tenant}-#{business.id}" }

    it 'searches with correct parameters' do
      expected_body = {
        query: { bool: { must: [{ terms: { "__CONTENT_ID.keyword": content_ids } }] } },
        fields: ['__CONTENT_ID', '__ATUALIZADO_EM'],
        _source: false,
        size: 4000
      }

      expect(elasticsearch_client).to receive(:search).with(
        index: index_name,
        body: expected_body
      )

      worker.send(:search_in_elasticsearch, content_ids, tenant, business.id)
    end
  end
end
