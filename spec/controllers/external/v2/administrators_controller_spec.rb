require 'rails_helper'

RSpec.describe External::V2::AdministratorsController, type: :controller do
  before do
    @administrator = create(:administrator)
    @create_params = {
      name: "Admin user",
      email: "<EMAIL>",
      password: "@!*tb25p47Pw0Mq7xgE",
      password_confirmation: "@!*tb25p47Pw0Mq7xgE",
      approved: true
    }
  end

  describe 'GET #index' do
    it 'returns ok status' do
      request.headers['ADMIN_TOKEN'] = @administrator.authorization_token
      request.headers['ADMIN_EMAIL'] = @administrator.email
      get :index, format: :json
      expect(response).to have_http_status(:ok)
    end

    it 'returns unauthorized status' do
      request.headers['ADMIN_TOKEN'] = nil
      get :index, format: :json
      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'GET #show' do
    it 'returns ok status' do
      request.headers['ADMIN_TOKEN'] = @administrator.authorization_token
      request.headers['ADMIN_EMAIL'] = @administrator.email
      get :show, format: :json, params: {
        id: @administrator.id
      }
      expect(response).to have_http_status(:ok)
    end

    it 'returns unauthorized status' do
      request.headers['ADMIN_TOKEN'] = nil
      get :show, format: :json, params: {
        id: @administrator.id
      }
      expect(response).to have_http_status(:unauthorized)
    end

    it 'returns not_found status' do
      request.headers['ADMIN_TOKEN'] = @administrator.authorization_token
      request.headers['ADMIN_EMAIL'] = @administrator.email
      get :show, format: :json, params: {
          id: 'id_fake'
        }
      expect(response).to have_http_status(:not_found)
    end
  end

  describe 'POST #create' do
    it 'returns ok status' do
      request.headers['ADMIN_TOKEN'] = @administrator.authorization_token
      request.headers['ADMIN_EMAIL'] = @administrator.email
      post :create, format: :json, params: @create_params
      expect(response).to have_http_status(:created)
    end

    it 'returns unauthorized status' do
      request.headers['ADMIN_TOKEN'] = nil
      post :create, format: :json, params: @create_params
      expect(response).to have_http_status(:unauthorized)
    end

    it 'returns required email error' do
      request.headers['ADMIN_TOKEN'] = @administrator.authorization_token
      request.headers['ADMIN_EMAIL'] = @administrator.email
      @create_params[:email] = nil
      post :create, format: :json, params: @create_params
      expect(response.body).to eq("{\"errors\":[\"Email deve ser preenchido\"]}")
    end
  end

  describe 'PATCH/PUT #update' do
    it 'returns ok when update administrator email' do
      request.headers['ADMIN_TOKEN'] = @administrator.authorization_token
      request.headers['ADMIN_EMAIL'] = @administrator.email
      patch :update, format: :json, params: {
        email: '<EMAIL>',
        id: @administrator.id
      }
      expect(response).to have_http_status(:ok)
    end

    it 'returns unauthorized status' do
      request.headers['ADMIN_TOKEN'] = nil
      patch :update, format: :json, params: {
        name: 'Test name',
        id: @administrator.id
      }
      expect(response).to have_http_status(:unauthorized)
    end

    describe 'update administrator name' do
      it 'updating administrator name when provider is email' do
        request.headers['ADMIN_TOKEN'] = @administrator.authorization_token
        request.headers['ADMIN_EMAIL'] = @administrator.email
        patch :update, format: :json, params: {
          name: 'Test name',
          id: @administrator.id
        }
        expect(response).to have_http_status(:ok)
      end

      describe 'error updating administrator name' do
        before do
          @administrator.update(provider: "entra_id")
        end

        it 'when provider is not email' do
          request.headers['ADMIN_TOKEN'] = @administrator.authorization_token
          request.headers['ADMIN_EMAIL'] = @administrator.email
          patch :update, format: :json, params: {
            name: 'Test name',
            id: @administrator.id
          }
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end
  end
end
