require 'rails_helper'

RSpec.describe ProviderValidation, type: :controller do
  controller(ApplicationController) do
    include ProviderValidation

    def create
      return unless validate_provider!
      render json: { success: true }
    end
  end

  describe '#validate_provider!' do
    context 'with valid providers' do
      it 'when provider is equal to email' do
        post :create, params: { provider: 'email' }
        expect(response).to have_http_status(:ok)
      end

      it 'when provider is equal to google_oauth2' do
        post :create, params: { provider: 'google_oauth2' }
        expect(response).to have_http_status(:ok)
      end

      it 'when provider is equal to entra_id' do
        post :create, params: { provider: 'entra_id' }
        expect(response).to have_http_status(:ok)
      end

      it 'when provider is equal to openid_connect' do
        post :create, params: { provider: 'openid_connect' }
        expect(response).to have_http_status(:ok)
      end

      it 'allows blank provider' do
        post :create, params: { provider: nil }
        expect(response).to have_http_status(:ok)
      end

      it 'allows empty string provider' do
        post :create, params: { provider: '' }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid providers' do
      it "rejects invalid provider" do
        post :create, params: { provider: 'invalid' }
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns correct error message for v1 controllers' do
        allow(controller.class).to receive(:name).and_return('External::UsersController')
        post :create, params: { provider: 'invalid' }
        expect(JSON.parse(response.body)['errors']).to eq(I18n.t('api.v1.users.errors.invalid_provider'))
      end

      it 'returns correct error message for v2 controllers' do
        allow(controller.class).to receive(:name).and_return('External::V2::UsersController')
        post :create, params: { provider: 'invalid' }
        expect(JSON.parse(response.body)['errors']).to eq(I18n.t('api.v2.users.errors.invalid_provider'))
      end
    end
  end
end
