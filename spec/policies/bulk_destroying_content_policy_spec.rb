require 'rails_helper'
require 'pundit/rspec'

RSpec.describe BulkDestroyingContentPolicy, type: :policy do
  before do
    @admin = create(:administrator, name: '<PERSON><PERSON>')
  end

  context 'when tenant is public' do
    before { allow(Apartment::Tenant).to receive(:current).and_return('public') }

    it 'permits index' do
      policy = described_class.new(@admin, nil)
      expect(policy.index?).to be_truthy
    end

    it 'permits show' do
      policy = described_class.new(@admin, nil)
      expect(policy.show?).to be_truthy
    end

    it 'permits destroy_all_contents' do
      policy = described_class.new(@admin, nil)
      expect(policy.destroy_all_contents?).to be_truthy
    end

    it 'permits destroy_selected_contents' do
      policy = described_class.new(@admin, nil)
      expect(policy.destroy_selected_contents?).to be_truthy
    end

    it 'permits process_orphans' do
      policy = described_class.new(@admin, nil)
      expect(policy.process_orphans?).to be_truthy
    end
  end

  context 'when tenant is not public' do
    before { allow(Apartment::Tenant).to receive(:current).and_return('test') }

    it 'forbids index' do
      policy = described_class.new(@admin, nil)
      expect(policy.index?).to be_falsy
    end

    it 'forbids show' do
      policy = described_class.new(@admin, nil)
      expect(policy.show?).to be_falsy
    end

    it 'forbids destroy_all_contents' do
      policy = described_class.new(@admin, nil)
      expect(policy.destroy_all_contents?).to be_falsy
    end

    it 'forbids destroy_selected_contents' do
      policy = described_class.new(@admin, nil)
      expect(policy.destroy_selected_contents?).to be_falsy
    end

    it 'forbids process_orphans' do
      policy = described_class.new(@admin, nil)
      expect(policy.process_orphans?).to be_falsy
    end
  end
end
