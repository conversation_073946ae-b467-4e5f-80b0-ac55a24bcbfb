require 'rails_helper'
require 'pundit/rspec'

RSpec.describe CompanyPolicy, type: :policy do
  before do
    @admin = create(:administrator, name: 'Foo')
  end

  context 'when tenant is public' do
    before { allow(Apartment::Tenant).to receive(:current).and_return('public') }

    it 'permits index' do
      policy = described_class.new(@admin, nil)
      expect(policy.index?).to be_truthy
    end

    it 'permits create' do
      policy = described_class.new(@admin, nil)
      expect(policy.create?).to be_truthy
    end

    it 'permits update' do
      policy = described_class.new(@admin, nil)
      expect(policy.update?).to be_truthy
    end

    it 'permits destroy' do
      policy = described_class.new(@admin, nil)
      expect(policy.destroy?).to be_truthy
    end
  end

  context 'when tenant is not public' do
    before { allow(Apartment::Tenant).to receive(:current).and_return('test') }

    it 'forbids index' do
      policy = described_class.new(@admin, nil)
      expect(policy.index?).to be_falsy
    end

    it 'forbids create' do
      policy = described_class.new(@admin, nil)
      expect(policy.create?).to be_falsy
    end

    it 'forbids update' do
      policy = described_class.new(@admin, nil)
      expect(policy.update?).to be_falsy
    end

    it 'forbids destroy' do
      policy = described_class.new(@admin, nil)
      expect(policy.destroy?).to be_falsy
    end
  end
end
