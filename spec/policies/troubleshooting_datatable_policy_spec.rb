require 'rails_helper'
require 'pundit/rspec'

RSpec.describe TroubleshootingDatatablePolicy, type: :policy do
  before do
    @admin = create(:administrator, name: '<PERSON><PERSON>')
  end

  context 'when tenant is public' do
    before { allow(Apartment::Tenant).to receive(:current).and_return('public') }

    it 'permits index' do
      policy = described_class.new(@admin, nil)
      expect(policy.index?).to be_truthy
    end
  end

  context 'when tenant is not public' do
    before { allow(Apartment::Tenant).to receive(:current).and_return('another_tenant') }

    it 'forbids index' do
      policy = described_class.new(@admin, nil)
      expect(policy.index?).to be_falsy
    end
  end
end
