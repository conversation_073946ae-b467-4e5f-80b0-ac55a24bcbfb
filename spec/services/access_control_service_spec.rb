require 'rails_helper'

RSpec.describe AccessControlService, type: :service do
  let(:business) { create(:business, :with_dependencies) }
  let(:user) { create(:user) }
  let(:step) { create(:step, business: business) }
  let(:template) { create(:template, :with_field) }
  let(:contents) { Content.where(business: business) }

  before do
    create(:step_template, step: step, template: template, order: 0)
  end

  describe '#build_condition_clause' do
    let(:service) { described_class.new(business.id, user, contents) }

    context 'for regular fields with contains operator' do
      let(:field) { create(:field, type: :text, template: template) }

      it 'uses ILIKE operator for text fields' do
        result = service.send(:build_condition_clause, step, field, 'contains', 'test')
        expect(result).to include('ILIKE')
        expect(result).to include('$$test$$')
      end
    end

    context 'for multiple fields with contains operator' do
      let(:field) { create(:field, type: :multiple, template: template) }

      it 'uses JSON contains operator for multiple fields' do
        result = service.send(:build_condition_clause, step, field, 'contains', 'test')
        expect(result).to include('?')
        expect(result).to include('$$test$$')
      end

      it 'handles array values correctly using ?& operator' do
        result = service.send(:build_condition_clause, step, field, 'contains', ['test1', 'test2'])
        expect(result).to include('?&')
        expect(result).to include('ARRAY[$$test1$$, $$test2$$]::text[]')
      end
    end

    context 'for multiple_reference fields with not_contains operator' do
      let(:field) { create(:field, :multiple_reference_type, template: template) }

      it 'uses JSON not contains operator for not_contains' do
        result = service.send(:build_condition_clause, step, field, 'not_contains', 'test')
        expect(result).to include('NOT (')
        expect(result).to include('$$test$$')
      end

      it 'handles array values correctly for not_contains using negated ?& operator' do
        result = service.send(:build_condition_clause, step, field, 'not_contains', ['test1', 'test2'])
        expect(result).to include('NOT (')
        expect(result).to include('?&')
        expect(result).to include('ARRAY[$$test1$$, $$test2$$]')
      end
    end

    context 'for upload fields with contains operator' do
      let(:field) { create(:field, type: :upload, template: template) }

      it 'uses JSON contains operator for upload fields' do
        result = service.send(:build_condition_clause, step, field, 'contains', 'file.pdf')
        expect(result).to include('?')
        expect(result).to include('$$file.pdf$$')
      end
    end
  end

  describe '#field_has_multiple_values?' do
    let(:service) { described_class.new(business.id, user, contents) }

    it 'returns true for multiple field type' do
      field = create(:field, type: :multiple, template: template)
      expect(service.send(:field_has_multiple_values?, field)).to be true
    end

    it 'returns true for multiple_reference field type' do
      field = create(:field, :multiple_reference_type, template: template)
      expect(service.send(:field_has_multiple_values?, field)).to be true
    end

    it 'returns true for upload field type' do
      field = create(:field, type: :upload, template: template)
      expect(service.send(:field_has_multiple_values?, field)).to be true
    end

    it 'returns false for text field type' do
      field = create(:field, type: :text, template: template)
      expect(service.send(:field_has_multiple_values?, field)).to be false
    end

    it 'returns false for reference field type' do
      field = create(:field, :reference_type, template: template)
      expect(service.send(:field_has_multiple_values?, field)).to be false
    end
  end

  describe '#build_array_condition' do
    let(:service) { described_class.new(business.id, user, contents) }
    let(:field) { create(:field, type: :multiple, template: template) }

    context 'for contains operator' do
      it 'builds correct SQL for single value' do
        result = service.send(:build_array_condition, step, field, 'contains', 'test')
        expect(result).to include('?')
        expect(result).to include('$$test$$')
      end

      it 'builds correct SQL for array value using ?& operator' do
        result = service.send(:build_array_condition, step, field, 'contains', ['test1', 'test2'])
        expect(result).to include('?&')
        expect(result).to include('ARRAY[$$test1$$, $$test2$$]')
      end
    end

    context 'for not_contains operator' do
      it 'builds correct SQL for single value' do
        result = service.send(:build_array_condition, step, field, 'not_contains', 'test')
        expect(result).to include('NOT (')
        expect(result).to include('$$test$$')
      end

      it 'builds correct SQL for array value using negated ?& operator' do
        result = service.send(:build_array_condition, step, field, 'not_contains', ['test1', 'test2'])
        expect(result).to include('NOT (')
        expect(result).to include('?&')
        expect(result).to include('ARRAY[$$test1$$, $$test2$$]')
      end
    end
  end
end
