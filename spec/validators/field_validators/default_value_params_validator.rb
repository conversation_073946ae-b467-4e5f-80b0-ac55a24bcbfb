require 'rails_helper'

RSpec.describe FieldValidators::DefaultValueParamsValidator do
  let(:action) { described_class.new(default_value) }

  describe '#perform' do
    context 'when default value should be string' do
      context 'when default value is empty' do
        let(:default_value) { '' }
        let(:type) { 'text' }
        let(:permitted) { :default_value }

        it 'return the permitted attribute por permit params method' do
          expect(action.perform(type)).to eq(permitted)
        end
      end

      context 'when default value is nil' do
        let(:default_value) { nil }
        let(:type) { 'text' }
        let(:permitted) { :default_value }

        it 'return the permitted attribute por permit params method' do
          expect(action.perform(type)).to eq(permitted)
        end
      end
    end

    context 'when default value should be object' do
      context 'when default value is empty' do
        let(:default_value) { {} }
        let(:type) { 'link' }
        let(:permitted) { { :default_value => {} } }

        it 'return the permitted attribute por permit params method' do
          expect(action.perform(type)).to eq(permitted)
        end
      end

      context 'when default value is nil' do
        let(:default_value) { nil }
        let(:type) { 'link' }
        let(:permitted) { { :default_value => {} } }

        it 'return the permitted attribute por permit params method' do
          expect(action.perform(type)).to eq(permitted)
        end
      end
    end

    context 'when default value should be array' do
      context 'when default value is empty' do
        let(:default_value) { [] }
        let(:type) { 'multiple' }
        let(:permitted) { { :default_value => [] } }

        it 'return the permitted attribute por permit params method' do
          expect(action.perform(type)).to eq(permitted)
        end
      end

      context 'when default value is nil' do
        let(:default_value) { nil }
        let(:type) { 'multiple' }
        let(:permitted) { { :default_value => [] } }

        it 'return the permitted attribute por permit params method' do
          expect(action.perform(type)).to eq(permitted)
        end
      end
    end


    context 'when type is in agreement with the dafault value' do
      context 'when default value should be string' do
        let(:default_value) { 'string value' }
        let(:type) { 'text' }
        let(:permitted) { :default_value }

        it 'return the permitted attribute por permit params method' do
          expect(action.perform(type)).to eq(permitted)
        end
      end

      context 'when default value should be array' do
        let(:default_value) { ['value 1', 'value 2', 'value 3'] }
        let(:type) { 'multiple' }
        let(:permitted) { { :default_value => [] } }

        it 'return the permitted attribute por permit params method' do
          expect(action.perform(type)).to eq(permitted)
        end
      end

      context 'when default value should be hash' do
        let(:default_value) { { 'key': 'value 1', 'key 2': 'value 2', 'key 3': 'value 3' } }
        let(:type) { 'link' }
        let(:permitted) { { :default_value => {} } }

        it 'return the permitted attribute por permit params method' do
          expect(action.perform(type)).to eq(permitted)
        end
      end
    end

    context 'when type is not in agreement with the dafault value' do
      context 'when default value should be string' do
        let(:default_value) { 'string value' }
        let(:type) { 'link' }

        it 'return the permitted attribute por permit params method' do
          expect{ action.perform(type) }.to raise_error(FieldsErrors::FieldDefaultValueMatchTypeError)
        end
      end

      context 'when default value should be array' do
        let(:default_value) { ['value 1', 'value 2', 'value 3'] }
        let(:type) { 'text' }

        it 'return the permitted attribute por permit params method' do
          expect{ action.perform(type) }.to raise_error(FieldsErrors::FieldDefaultValueMatchTypeError)
        end
      end

      context 'when default value should be hash' do
        let(:default_value) { 'string value' }
        let(:type) { 'multiple' }

        it 'return the permitted attribute por permit params method' do
          expect{ action.perform(type) }.to raise_error(FieldsErrors::FieldDefaultValueMatchTypeError)
        end
      end
    end
  end
end