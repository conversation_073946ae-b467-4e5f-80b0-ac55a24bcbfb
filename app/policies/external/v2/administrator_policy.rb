class External::V2::AdministratorPolicy < ApplicationPolicy
  def initialize(credentials)
    @credentials = credentials
  end

  def index?
    valid_credentials?
  end

  alias_method :show?, :index?
  alias_method :create?, :index?
  alias_method :update?, :index?

  private

  def valid_credentials?
    Administrator.find_by(
      authorization_token: @credentials[:token],
      email: @credentials[:admin_email]
    )
  end
end
