class External::V2::UserPolicy < ApplicationPolicy
  def initialize(credentials)
    @credentials = credentials
  end

  def create?
    valid_credentials?
  end

  alias_method :update?, :create?
  alias_method :destroy?, :create?
  alias_method :show?, :create?
  alias_method :index?, :create?

  private

  def valid_credentials?
    Administrator.find_by(
      authorization_token: @credentials[:token],
      email: @credentials[:admin_email]
    )
  end
end
