class BaseXmlBuilder
  def initialize(business, parameters = {})
    @business = business
    @parameters = parameters
  end

  def generate
    builder = Nokogiri::XML::Builder.new(encoding: 'UTF-8') do |xml|
      xml.fourmdg(tenant: Apartment::Tenant.current) do
        xml.business(guid: @business.id, name: @business.name, group_guid: @business.business_group_id) do
          xml_contents(xml, current_user) if include_content?
          xml_structures(xml) if include_structure?
        end
      end
    end

    # builder.doc.root.to_s
    builder.to_xml
  end

  def current_user
    @current_user ||= @parameters[:current_user]
  end

  protected

  def get_answer_value(answer, field)
    value = answer.values[field.id]
    return nil if value.blank?

    case
    when value.is_a?(Array)
      value = value.join('|')
    when value.is_a?(Hash)
      value = value.to_json
    end

    value
  rescue
    nil
  end

  def xml_template(xml, template_id, answer)
    xml.template(guid: template_id) do
      Field.kept.where(template_id: template_id).where(type: :sub_business).select(:id, :label, :deleted_at, :reference_sub_business_id).each do |field|
        xml.subbusiness(guid: field.reference_sub_business_id) do
          answer.sub_contents.where(business_id: field.reference_sub_business_id).pluck(:id).each do |sub_content_id|
            xml.content(guid: sub_content_id)
          end
        end
      end

      Field.where(template_id: template_id).where.not(type: :sub_business).select(:id, :label, :deleted_at).each do |field|
        next unless answer.values&.keys&.include?(field.id)

        values = answer.values[field.id]
        xml.field(guid: field.id, label: field.label, value: get_answer_value(answer, field)) unless field.discarded?
        xml.field(guid: field.id, label: field.label, value: get_answer_value(answer, field), deleted: true) if field.discarded?
      end
    end
  end

  def xml_structures(xml)
    xml.structures do
      xml.steps do
        @business.active_steps.each do |step|
          xml.step(guid: step.id, label: step.name) do
            step.templates.each do |template|
              xml.template(guid: template.id, name: template.name, description: template.description, variable: template.variable)
            end
          end
        end
      end

      xml.templates do
        @business.active_steps.each do |step|
          step.templates.each do |template|
            xml.template(guid: template.id, variable: template.variable) do
              template.active_fields.each do |field|
                xml.field(guid: field.id, type: field.type, size: field.size, height: field.height, tooltip: field.tooltip, order: field.order,
                          required: field.required, enabled: field.enabled, visible: field.visible, label: field.label,
                          input_variable: field.input_variable, output_variable: field.output_variable,
                          show_on_list: ShowOnListField.exists?(business_id: @business.id, field_id: field.id), show_on_form: field.show_on_form,
                          reference_business_guid: field.reference_business_id, reference_field_guid: field.reference_field_id,
                          reference_sub_business_guid: field.reference_sub_business_id) do
                  field.options&.each do |option|
                    xml.option(label: option['label'], value: option['value'])
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
