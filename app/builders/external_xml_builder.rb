class ExternalXmlBuilder < BaseXmlBuilder
  def per_page
    100
  end

  def filtered_contents
    records = Content.not_draft.where(business_id: @business.id)
    records = records.with_discarded.discarded if @parameters[:deleted]
    records = records.where('contents.updated_at >= ?', @parameters[:start].in_time_zone) if @parameters[:start].present?
    records = records.where('contents.updated_at <= ?', @parameters[:end].in_time_zone) if @parameters[:end].present?
    records = records.where(id: @parameters[:content_id]) if @parameters[:content_id].present?
    records = records.where(id: @parameters[:sub_content_id]) if @parameters[:sub_content_id].present?
    records = records.where(parent_id: @parameters[:parent_id]) if @parameters[:parent_id].present?
    records.paginate(page: (@parameters[:page] || 1), per_page: per_page)
  end

  protected

  def include_content?
    ActiveRecord::Type::Boolean.new.deserialize(@parameters[:contents])
  end

  def include_structure?
    ActiveRecord::Type::Boolean.new.deserialize(@parameters[:structures])
  end

  def xml_contents(xml, current_user)
    xml.contents do
      filtered_contents.select(:id, :updated_at, :parent_id, :created_by_id, :current_answer_id).each do |content|
        content_attributes = { guid: content.id, updated_at: content.updated_at }

        if content.created_by_id
          content_attributes[:created_by_id] = content.created_by_id
          content_attributes[:created_by_name] = User.find_by(id: content.created_by_id).try(:name)

          step = Answer.find_by(id: content.current_answer_id).try(:step)
          content_attributes[:current_step_id] = step.try(:id)
          content_attributes[:current_step_name] = step.try(:name)
        end

        content_attributes[:parent_id] = content.parent_id if content.parent_id.present?

        xml.content(content_attributes) do
          Answer.joins(:step).where(content_id: content.id).order('steps.order').each do |answer|
            xml.step(label: answer.step.name, user: answer.user_id, current_user: current_user, guid: answer.step_id) do
              answer.step.templates.pluck(:id).each do |template_id|
                xml_template(xml, template_id, answer)
              end
            end
          end
        end
      end
    end
  end
end
