class ValidationXmlBuilder < BaseXmlBuilder
  def initialize(answer, current_user)
    @answer = answer
    @content = @answer.content
    @current_user = current_user

    super @content.business
  end

  protected

  def include_content?
    true
  end

  def include_structure?
    true
  end

  def xml_step(xml, answer)
    xml.step(
      label: answer.step.name,
      guid: answer.step_id,
      updated_at: answer.updated_at,
      current_user: @current_user&.id,
      user: answer.user_id,
      user_id: answer.user_id,
      user_name: answer.user&.name,
      user_email: answer.user&.email,
      status: answer.status,
    ) do
      answer.step.templates.pluck(:id).each do |template_id|
        xml_template(xml, template_id, answer)
      end
    end
  end

  def xml_contents(xml, _current_user = nil)
    xml.contents do
      xml.content(
        guid: @content.id,
        parent_id: @content.parent_id,
        updated_at: @content.updated_at,
        current_step: @answer.step_id,
        created_by_id: @content.created_by_id,
        created_by_name: @content.created_by&.name,
        created_by_email: @content.created_by&.email,
        status: @content.status,
      ) do
        @content.answers.each do |answer|
          answer.id == @answer.id ? xml_step(xml, @answer) : xml_step(xml, answer)
        end
      end
    end
  end
end
