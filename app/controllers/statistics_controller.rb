class StatisticsController < ApplicationController
  before_action :authenticate_administrator!, except: %i[index show]
  before_action :authenticate_member!, only: %i[index show]
  around_action :check_access_tenant, only: %i[index]
  before_action :check_for_permission

  def index
    respond_to do |format|
      format.datatable do
        render json: StatisticDatatable.new(params, current_user: current_user), status: :ok
      end
      format.json do
        statistics = Statistic.all
        statistics = statistics.joins(:departments).where(departments: { id: current_user.department_ids }) if current_user.present?

        @records = statistics
      end
    end
  end

  def show
    @statistic = Statistic.find(params[:id])
  end

  def create
    service = StatisticService.new(statistic_params)

    service.create

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = StatisticService.new(statistic_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = StatisticService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def check_for_permission
    return render json: { errors: I18n.t('forbidden', scope: 'activerecord.errors.messages') }, status: :forbidden if current_administrator.nil? && current_user.block_menus.include?('statistics')
  end

  def statistic_params
    params.permit(:title, :description, department_ids: [])
  end
end
