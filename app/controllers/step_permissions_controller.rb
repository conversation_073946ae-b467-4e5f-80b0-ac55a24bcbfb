class StepPermissionsController < ApplicationController
  before_action :authenticate_member!, except: %i[for_current_user]
  before_action :authenticate_user!, only: %i[for_current_user]
  before_action :authenticate_administrator!, except: %i[index for_current_user]

  include SkipTranslation

  def index
    @records = StepPermissionSearcher.new(search_params).search
  end

  def for_current_user
    @records = StepPermissionSearcher.new(search_params.merge(user_id: current_user.id)).search

    @records = @records.select(:step_id, :scope).group_by { |d| d[:step_id] }
    @records = @records.transform_values { |v| v.map(&:scope) }

    render json: @records
  end

  def create
    service = StepPermissionService.new(step_permission_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = StepPermissionService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def step_permission_params
    params.permit(:step_id, :user_id, :department_id, :scope)
  end

  def search_params
    params.permit(:user_id, :business_id, :scope)
  end
end
