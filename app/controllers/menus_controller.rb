class MenusController < ApplicationController
  before_action :set_company_enable_internationalization, only: %i[index]

  def index
    groups = BusinessGroup.kept.includes(businesses: :fields).order(:name)

    menus = groups.map do |group|
      valid_businesses = group.businesses.kept.with_permission_for_user(current_user).order(:name).distinct
      {
        id: group.id,
        icon: group.icon,
        name: group.translated_attribute('name', current_administrator, @company_enable_internationalization),
        children: valid_businesses.map { |business| { id: business.id, name: business.translated_attribute('name', current_administrator, @company_enable_internationalization), icon: business.icon, children: subbusiness(business) } }
      }
    end
    menus = menus.select { |m| m[:children].present? }

    render json: menus
  end

  private

  def subbusiness(business)
    subbusiness_ids = Field.for_business(business.id).where(type: :sub_business, deleted_at: nil).pluck(:reference_sub_business_id)
    Business.kept.where(id: subbusiness_ids).map { |subbusiness| { id: subbusiness.id, icon: subbusiness.icon, name: subbusiness.translated_attribute('name', current_administrator, @company_enable_internationalization) } }
  end
end
