class StepsController < ApplicationController
  include SkipTranslation

  before_action :authenticate_administrator!, except: %i[index show]
  before_action :authenticate_member!, only: :index
  before_action :load_business, only: %i[show]
  before_action :set_company_enable_internationalization, only: %i[index show]

  def index
    searcher = StepSearcher.new(params)
    @records = searcher.search
  end

  def show
    @step = @business.steps.find(params[:id])
  end

  def create
    service = StepService.new(step_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = StepService.new(step_update_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = StepService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def activate
    service = StepService.new

    service.restore(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def load_business
    @business = Business.find(params[:business_id])
  end

  def step_params
    params.permit(:name, :description, :business_id, :verification_url, :validation_url, :success_message, :order, :send_email_to_all_with_access,
                  :send_email_to_user_who_registered, :require_credentials, :require_credentials_use_any_email, :not_editable, steps_to_change_ids: [], step_for_revision_ids: [])
  end

  def step_update_params
    step_params.merge(params.permit(:order))
  end
end
