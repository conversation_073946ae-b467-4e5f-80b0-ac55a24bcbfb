class TroubleshootingsController < ApplicationController
  before_action :authenticate_member!

  def index
    respond_to do |format|
      format.datatable do
        @troubleshooting_datatable = TroubleshootingDatatable.new(params, current_user: current_user)
        authorize @troubleshooting_datatable
        render json: @troubleshooting_datatable, status: :ok
      end
      format.json do
        @troubleshootings = []
      end
    end
  end
end
