class ShowOnListFieldsController < ApplicationController
  include SkipTranslation
  include ShowOnListFieldHelper

  before_action :authenticate_administrator!, except: %i[index]
  before_action :authenticate_member!, only: %i[index]
  before_action :set_company_enable_internationalization, only: %i[index]

  def index
    @records = fields(params[:business_id], params[:include_parent_keys])
  end

  def create
    service = ShowOnListFieldService.new(create_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = ShowOnListFieldService.new(update_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = ShowOnListFieldService.new

    ShowOnListField.where(field_id: params[:id]).destroy_all

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def bulk_create
    service = ShowOnListFieldService.new(bulk_create_params)
    service.bulk_create

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def create_params
    params.permit(:business_id, :field_id, :order, :step_id)
  end

  def update_params
    params.permit(:order, :order_contents)
  end

  def bulk_create_params
    params.permit(:business_id, data: %i[business_id field_id order_contents step_id order])
  end

end
