module External
  class SubContentsController < BaseController
    before_action :validate_api_key

    def index
      @business = Business.sub_business.find_by(id: params[:sub_business_id])

      return head :not_found unless @business

      service = ExternalXmlBuilder.new(@business, params.merge('current_user' => current_user))

      response.header['total-records'] = service.filtered_contents.count&.to_s
      response.header['pages'] = ((service.filtered_contents.count / service.per_page) + 1)&.to_s

      render xml: service.generate, status: :ok
    end
  end
end
