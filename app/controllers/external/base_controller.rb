module External
  class BaseController < ::ActionController::API
    include ActionController::MimeResponds
    include Pundit::Authorization

    def authenticate_with_token!
      token = request.headers['HTTP_USER_TOKEN'] || request.headers['USER_TOKEN']
      user_email = request.headers['HTTP_USER_EMAIL'] || request.headers['USER_EMAIL']

      render json: { error: 'Unauthorized' }, status: :unauthorized if token.blank? || !valid_token?(token, user_email)
    end

    def administrator_credentials
      token = request.headers['HTTP_ADMIN_TOKEN'] || request.headers['ADMIN_TOKEN']
      admin_email = request.headers['HTTP_ADMIN_EMAIL'] || request.headers['ADMIN_EMAIL']

      { token: token, admin_email: admin_email }
    end

    private

    def valid_token?(token, user_email)
      user = User.find_by(authorization_token: token, email: user_email)
      if user.present?
        @current_user = user
        true
      else
        false
      end
    end

    def validate_admin_api_key
      return if valid_subdomain_admin? && valid_token_admin?

      render json: { error: 'unauthorized' }, status: :unauthorized
    end

    def valid_subdomain_admin?
      request.subdomain == 'admin'
    end

    def valid_token_admin?
      header_authorization_present? && request.headers['Authorization'] == "Basic #{ENV['EXTERNAL_API_ADMIN_KEY']}"
    end

    def header_authorization_present?
      request.headers['Authorization'].present?
    end

    def validate_api_key
      current_company = Company.current

      return unless request.headers['Authorization'] != "Basic #{current_company&.api_key}"

      respond_to do |format|
        format.json do
          render json: { error: 'unauthorized' }, status: :unauthorized
        end

        format.xml do
          render xml: { error: 'unauthorized' }, status: :unauthorized
        end
      end
    end

    def info_for_paper_trail
      { whodunnit_type: nil, ip: remote_ip(request) }
    end

    def remote_ip(request)
      request.headers['HTTP_X_REAL_IP'] || request.remote_ip
    end
  end
end
