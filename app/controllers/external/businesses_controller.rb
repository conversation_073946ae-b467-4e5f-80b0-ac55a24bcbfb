module External
  class BusinessesController < BaseController
    before_action :validate_api_key

    DEFAULT_USER_WHO_DELETED_EMAIL = '<EMAIL>'.freeze

    respond_to :xml

    def create
      respond_with do |format|
        format.xml do
          service = External::ContentService.new(request.body.read, request.remote_ip)

          service.save

          if service.success
            render xml: { success: true, guids: service.records.map(&:id), warning: service.errors }, status: :created
          else
            render xml: { errors: service.errors }, status: :unprocessable_entity
          end
        end
      end
    end

    def update
      respond_with do |format|
        format.xml do
          service = External::ContentService.new(request.body.read, request.remote_ip)

          service.save

          if service.success
            render xml: { success: true, guids: service.records.map(&:id), warning: service.errors }, status: :ok
          else
            render xml: { errors: service.errors }, status: :unprocessable_entity
          end
        end
      end
    end

    def destroy
      @content = Content.find_by(id: params[:id])

      return render xml: { success: false, errors: 'Record not found' }, status: :not_found unless @content

      @user_who_deleted = User.find_by(id: params[:user_who_deleted_id]) || User.find_by(email: DEFAULT_USER_WHO_DELETED_EMAIL) || User.create!(name: 'Integração', email: DEFAULT_USER_WHO_DELETED_EMAIL, password: '&~S{M93>m9rP}Y')

      service = ::ContentService.new

      service.destroy(@content.id, destroy_params.merge(current_user: @user_who_deleted, current_user_ip: request.remote_ip))

      if service.success
        render xml: { success: true }, status: :ok
      else
        render xml: { errors: service.errors }, status: :bad_request
      end
    end

    private

    def destroy_params
      params.permit(:deletion_reason)
    end
  end
end
