module External
  class NotificationsController < BaseController
    before_action :validate_api_key
    before_action :set_default_response_format

    def show
      @record = Notification.kept.find(params[:id])
    end

    def create
      service = NotificationService.new(create_params)

      service.create

      if service.success
        render json: { id: service.record.id }, status: :created
      else
        render json: { errors: service.errors }, status: :unprocessable_entity
      end
    end

    def update
      service = NotificationService.new(update_params)

      service.update(params[:id])

      if service.success
        head :ok
      else
        render json: { errors: service.errors }, status: :unprocessable_entity
      end
    end

    def destroy
      service = NotificationService.new

      service.discard(params[:id])

      head :no_content
    end

    private

    def update_params
      params.permit(:title, :message, destiny_users: [], destiny_departments: [])
    end

    def create_params
      params.permit(:title, :message, :user_id, destiny_users: [], destiny_departments: [])
    end

    def set_default_response_format
      request.format = :json
    end
  end
end
