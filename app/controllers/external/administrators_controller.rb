module External
  class AdministratorsController < BaseController
    before_action :validate_api_key
    before_action :set_paper_trail_whodunnit

    def create
      respond_to do |format|
        format.json do
          service = AdministratorService.new(administrator_params)

          service.create

          if service.success
            head :created
          else
            render json: { errors: service.errors }, status: :unprocessable_entity
          end
        end

        format.xml do
          render xml: { error: 'xml format is not supported' }, status: :not_acceptable
        end
      end
    end

    def destroy
      respond_to do |format|
        format.json do
          service = AdministratorService.new

          service.destroy(params[:id])

          if service.success
            head :no_content
          else
            render json: { errors: service.errors }, status: :bad_request
          end
        end

        format.xml do
          render xml: { error: 'xml format is not supported' }, status: :not_acceptable
        end
      end
    end

    def administrator_params
      params.permit(:name, :email, :password, :password_confirmation, :approved, :owner)
    end
  end
end
