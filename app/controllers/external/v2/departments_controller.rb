class External::V2::DepartmentsController < External::BaseController
  before_action :set_department, only: %i[show update]
  before_action :authorize_policy, only: %i[index show create update]

  def index
    if params[:limit].to_i > 500
      render json: { errors: I18n.t("api.v2.departments.errors.max_limit") }, status: :unprocessable_entity
      return
    end

    offset = params[:offset] || 0
    limit = params[:limit] || 10

    @total_departments = Department.count
    @total_next_departments = Department.offset(offset.to_i + limit.to_i).count
    @has_more_departments = @total_next_departments.positive?

    @departments = Department.offset(offset).limit(limit)
  end

  def show
    render json: { errors: I18n.t("api.v2.departments.errors.department_not_found") }, status: :not_found if @department.nil?
  end

  def create
    service = DepartmentService.new(department_params)

    service.create

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = DepartmentService.new(department_params)

    service.update(@department&.id)

    if service.success
      render json: { id: service.record.id }, status: :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  # will be used in the future
  # def destroy
  #   service = DepartmentService.new

  #   service.destroy(params[:id])

  #   if service.success
  #     head :no_content
  #   else
  #     render json: { errors: service.errors }, status: :bad_request
  #   end
  # end

  private

  def authorize_policy
    allowed_actions = %w[index? show? create? update? destroy?]
    method_name = "#{params[:action]}?"

    unless allowed_actions.include?(method_name)
      render json: { error: I18n.t("api.v2.departments.errors.unauthorized") }, status: :unauthorized
      return
    end

    policy = External::V2::DepartmentPolicy.new(administrator_credentials)
    unless policy.public_send(method_name)
      render json: { error: I18n.t("api.v2.departments.errors.unauthorized") }, status: :unauthorized
    end
  end

  def set_department
    @department = Department.find_by(id: params[:id])
  end

  def department_params
    params.permit(:name, :limited)
  end
end
