class External::V2::AdministratorsController < External::BaseController
  before_action :set_administrator, only: %i[show update]
  before_action :authorize_policy, only: %i[index show create update]

  def index
    if params[:limit].to_i > 500
      render json: { errors: I18n.t("api.v2.administrators.errors.max_limit") }, status: :unprocessable_entity
      return
    end

    offset = params[:offset] || 0
    limit = params[:limit] || 10

    @total_administrators = Administrator.count
    @total_next_administrators = Administrator.offset(offset.to_i + limit.to_i).count
    @has_more_administrators = @total_next_administrators.positive?

    @administrators = Administrator.offset(offset).limit(limit)
  end

  def show
    render json: { errors: I18n.t("api.v2.administrators.errors.administrator_not_found") }, status: :not_found if @administrator.nil?
  end

  def create
    service = AdministratorService.new(administrator_params)

    service.create

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = AdministratorService.new(administrator_params)

    service.update(@administrator&.id)

    if service.success
      render json: { id: service.record.id }, status: :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  private

  def authorize_policy
    allowed_actions = %w[index? show? create? update? destroy?]
    method_name = "#{params[:action]}?"

    unless allowed_actions.include?(method_name)
      render json: { error: I18n.t("api.v2.administrators.errors.unauthorized") }, status: :unauthorized
      return
    end

    policy = External::V2::AdministratorPolicy.new(administrator_credentials)
    unless policy.public_send(method_name)
      render json: { error: I18n.t("api.v2.administrators.errors.unauthorized") }, status: :unauthorized
    end
  end

  def set_administrator
    @administrator = Administrator.find_by(id: params[:id])
  end

  def administrator_params
    params.permit(:name, :email, :password, :password_confirmation, :approved, :owner)
  end
end
