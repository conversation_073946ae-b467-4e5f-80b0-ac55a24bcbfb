module External
  class AnswersController < BaseController
    before_action :validate_api_key

    def reject
      begin
        service = AnswerService.new(Hash.new.merge('remote_ip' => request.remote_ip))

        answer = service.reject_by_content_and_step_id(parameters['content_id'], parameters['step_id'], params['user_id'])

        if answer
          render json: { success: true, guids: [service.record.id], warning: service.errors }, status: :ok
        else
          render json: { errors: service.errors }, status: :unprocessable_entity
        end
      rescue StandardError => e
        render json: { errors: service.errors }, status: :unprocessable_entity
      end
    end

    def authorize
      begin
        service = AnswerService.new

        answer = service.authorize_by_content_and_step_id(parameters['content_id'], parameters['step_id'], params['user_id'])

        if answer
          render json: { success: true, guids: [service.record.id], warning: service.errors }, status: :ok
        else
          render json: { errors: service.errors }, status: :unprocessable_entity
        end
      rescue StandardError => e
        render json: { errors: service.errors }, status: :unprocessable_entity
      end
    end

    def changing
      begin
        service = AnswerService.new
        answer = service.in_changing(parameters['content_id'], parameters['step_id'])

        if answer
          render json: { success: true, guids: [service.record.id], warning: service.errors }, status: :ok
        else
          render json: { errors: service.errors }, status: :unprocessable_entity
        end
      rescue StandardError => e
        render json: { errors: service.errors }, status: :unprocessable_entity
      end
    end

    private

    def parameters
      {
        'content_id' => params['content_id'],
        'step_id' => params['id']
      }
    end
  end
end
