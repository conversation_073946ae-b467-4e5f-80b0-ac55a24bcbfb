class SearchController < ApplicationController
  before_action :authenticate_member!

  def index
    respond_to do |format|
      format.datatable do
        render json: SearchDatatable.new(params), status: :ok
      end
      format.json do
        @records = Search.all
      end
    end
  end

  def show
    @record = Search.find(params[:id])
  end

  def create
    service = SearchService.new(search_params)

    service.create

    if service.success?
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = SearchService.new(search_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = SearchService.new

    service.destroy(params[:id])

    if service.success?
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def search
    service = ElasticSearcherService.new(params)

    presenter = SearchPresenter.new(service.search, service.mapping_information, service.send(:search_source), service.search_body)

    render json: presenter.as_json, status: :ok
  rescue ElasticSearcherService::ServiceError => e
    render json: { errors: [e.message] }, status: :bad_request
  end

  def build_query
    query = query_builder_params.each do |e|
      %i[field_name value].each do |field|
        e[field] = e[field].to_s.gsub(/[()]/, '\\\\\0')
      end
    end
    render json: { query: Elasticsearch::Builders::QueryBuilder.build_legacy_query_string(query, operator) }
  end

  private

  def query_builder_params
    params.permit(criterions: %i[operator field_name value])[:criterions]
  end

  def operator
    params.permit(:operator)[:operator]
  end

  def search_params
    params.permit(:title, :description, :business_id, :query, :kind, field_ids: [], field_names: []).merge(user_id: current_user.id)
  end
end
