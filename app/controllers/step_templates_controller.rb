class StepTemplatesController < ApplicationController
  before_action :authenticate_administrator!

  def create
    service = StepTemplateService.new(step_template_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def order
    service = StepTemplateService.new(step_template_order_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = StepTemplateService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def step_template_params
    params.permit(:step_id, :template_id)
  end

  def step_template_order_params
    params.permit(:order)
  end
end
