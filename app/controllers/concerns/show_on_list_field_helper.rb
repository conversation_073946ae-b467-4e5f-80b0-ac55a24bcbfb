module ShowOnListFieldHelper
  extend ActiveSupport::Concern

  included do
    def fields(business_id, include_parent_keys = false)
      records = []

      if include_parent_keys
        business_father = fetch_business_father(business_id)
        records.concat(fetch_business_father_records(business_father, records.size)) if business_father
      end

      records.concat(fields_for_business(business_id))
    end
  end

  def fields_for_business(business_id)
    ShowOnListField.for_business(business_id)
  end

  def fetch_business_father(business_id)
    Business.find(business_id).parent
  end

  def build_show_on_list_field(field, step, size)
    {
      id: nil,
      order: size,
      order_contents: 'none',
      business_id: step.business.id,
      step_id: step.id,
      step_name: step.name,
      field_id: field.id,
      field_type: field.type,
      field_options: field.options,
      label: field.label,
    }
  end

  def fetch_business_father_records(business_father, size)
    business_father_first_step = business_father.steps.first
    Fields::GetKeyFieldsOfBusinessOrderByUpdatedAtQuery.call(business_father.id).map do |field|
      build_show_on_list_field(field, business_father_first_step, size)
    end
  end
end
