module QueryHelper
  extend ActiveSupport::Concern

  included do
    def build_answer_join(step)
      "J<PERSON><PERSON> answers a#{step.order} ON a#{step.order}.content_id = contents.id AND a#{step.order}.step_id = '#{step.id}'"
    end

    def build_parent_first_answer_join(business)
      step = business.parent&.steps&.first
      "J<PERSON><PERSON> answers pa0 ON pa0.content_id = contents.parent_id AND pa0.step_id = '#{step.id}'"
    end
  end
end
