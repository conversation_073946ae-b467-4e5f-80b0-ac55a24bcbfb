module ProviderValidation
  extend ActiveSupport::Concern

  VALID_PROVIDERS = %w[email google_oauth2 entra_id openid_connect].freeze

  private

  def validate_provider!
    return true if valid_provider?

    render json: { errors: provider_error_message }, status: :unprocessable_entity
    false
  end

  def valid_provider?
    VALID_PROVIDERS.include?(params[:provider]) || params[:provider].blank?
  end

  def provider_error_message
    api_version = self.class.name.include?('V2') ? 'v2' : 'v1'
    I18n.t("api.#{api_version}.users.errors.invalid_provider")
  end
end
