class WidgetsController < ApplicationController
  before_action :authenticate_administrator!
  before_action :authenticate_member!
  around_action :check_access_tenant

  def show
    @widget = Widget.find(params[:id])
  end

  def create
    service = WidgetService.new(widget_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = WidgetService.new(widget_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = WidgetService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def widget_params
    params.permit(:chart_type, :embedded_code, :size, :order, :search_id, :statistic_id, :category_field_id, :value_field_id, :category_agg, :value_agg)
  end
end
