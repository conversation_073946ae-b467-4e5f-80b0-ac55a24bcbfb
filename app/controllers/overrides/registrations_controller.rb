# :nocov:
module Overrides
  class RegistrationsController < DeviseTokenAuth::RegistrationsController
    def create
      return render json: { success: false, errors: I18n.t('devise.registrations.disabled') }, status: :unprocessable_entity unless Company.current.enable_signup

      super
    end

    def build_resource
      email = super

      @resource.limited = Company.current.limit_user_on_signup?
      @resource.department_ids = [@resource.department_ids, Company.current.default_department_id].compact.uniq
      @resource.skip_confirmation! if Company.current.bypass_approval?
      @resource.block_menus = Company.current.block_menus

      email
    end

    def render_create_error
      render json: { success: false, errors: current_user.errors.full_messages }, status: :unprocessable_entity
    end
  end
end
# :nocov:
