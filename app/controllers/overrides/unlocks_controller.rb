# :nocov:
module Overrides
  class UnlocksController < DeviseTokenAuth::UnlocksController
    protected

    def render_show_error
      if params.fetch(:redirect_url)
        redirect_to DeviseTokenAuth::Url.generate(params.fetch(:redirect_url), account_unlock_success: false)
      else
        super
      end
    end

    def after_unlock_path_for(_resource)
      params.fetch(:redirect_url)
    end
  end
end
# :nocov:
