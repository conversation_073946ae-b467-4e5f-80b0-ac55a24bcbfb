# :nocov:
module Overrides
  class SessionsController < DeviseTokenAuth::SessionsController
    protected

    def render_create_error_bad_credentials
      render_error(401, I18n.t(@resource ? "devise.failure.#{@resource.unauthenticated_message}" : 'devise_token_auth.sessions.bad_credentials'))
    end

    def render_create_error_not_confirmed
      error_message = current_user.confirmed? ? I18n.t('devise_token_auth.sessions.bad_credentials') : I18n.t('devise.failure.not_confirmed')
      render json: { success: false, errors: current_user.errors.empty? ? [error_message]: current_user.errors.full_messages }, status: :unauthorized
    end
  end
end
# :nocov:
