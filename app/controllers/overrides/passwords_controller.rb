# :nocov:
module Overrides
  class PasswordsController < DeviseTokenAuth::PasswordsController
    def create
      if User.find_by(email: params[:email]).try(:discarded?)
        render_discarded_error(params[:email])
      elsif User.find_by(email: params[:email]).try(:locked_at).nil?
        super
      else
        render_blocked_user_error(params[:email])
      end
    end

    protected

    def render_edit_error
      render plain: I18n.t('password_recovery', scope: 'activerecord.errors.messages'), status: :bad_request
    end

    def render_update_error
      render json: { success: false, errors: current_user.errors.full_messages }, status: :unprocessable_entity
    end

    def render_discarded_error(email)
      Rails.logger.error "[PasswordsController][discarded_user] - #{email}"

      render_update_success
    end

    def render_not_found_error
      Rails.logger.error "[PasswordsController][not_found] - #{I18n.t('devise_token_auth.passwords.send_paranoid_instructions', email: @email)}"

      render_update_success
    end

    def render_update_success
      render json: { success: true, message: I18n.t('devise.passwords.send_paranoid_instructions') }
    end

    def render_blocked_user_error(email)
      Rails.logger.error "[PasswordsController][Blocked user - Password change not allowed] - email: #{email}"

      render_update_success
    end

    def success_message(_name, _email)
      I18n.t('devise.passwords.send_paranoid_instructions')
    end

    def success_message(_name, _email)
      I18n.t('devise.passwords.send_paranoid_instructions')
    end
  end
end
# :nocov:
