class TranslationsController < ApplicationController
  before_action :authenticate_administrator!

  def index
    respond_to do |format|
      format.json do
        @records = Translation.all
        @records = @records.where(actable_id: params[:actable_id], actable_type: params[:actable_type]) if params[:actable_id].present? && params[:actable_type].present?
        @records = @records.order(:language)
      end
    end
  end

  def create
    service = TranslationService.new(translation_params)

    service.create

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = TranslationService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def migrate_data
    service = TranslationService.new

    service.migrate_data(params[:company_id], params[:language])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def translation_params
    params.permit(:language, :translated_text, :attribute_name, :actable_id, :actable_type)
  end
end
