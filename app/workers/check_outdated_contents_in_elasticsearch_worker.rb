# frozen_string_literal: true
class CheckOutdatedContentsInElasticsearchWorker
  include Sidekiq::Worker

  sidekiq_options queue: :elasticsearch_setup, lock: :until_executed, lock_ttl: 1.hour

  def perform
    companies = Company.where(use_elasticsearch: true).select(:subdomain, :last_checked_elasticsearch)
    args = companies.map { |company| [company.subdomain, company.last_checked_elasticsearch&.iso8601 ] }

    return if args.empty?

    CheckOutdatedContentsInElasticsearchForTenantWorker.perform_bulk(args)
  end
end
