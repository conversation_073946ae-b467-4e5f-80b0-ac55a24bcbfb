class CheckNotIndexedBusinessesKeysWorker
  include Sidekiq::Worker

  sidekiq_options retry: 0

  def perform(tenant = nil)
    return Company.pluck(:subdomain).each { |subdomain| CheckNotIndexedBusinessesKeysWorker.perform_async(subdomain) } if tenant.blank?

    Apartment::Tenant.switch! tenant

    businesses = Business.all
    businesses.find_each do |business|
      business.update_answer_index!
    end
  end
end
