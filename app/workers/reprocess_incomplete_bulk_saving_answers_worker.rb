class ReprocessIncompleteBulkSavingAnswersWorker
  include Sidekiq::Worker

  REPROCESS_USER_EMAIL = '<EMAIL>'.freeze

  sidekiq_options retry: 0, lock: :until_executed, lock_ttl: 1.day

  def perform(tenant = nil)
    return Company.pluck(:subdomain).each { |subdomain| ReprocessIncompleteBulkSavingAnswersWorker.perform_async(subdomain) } if tenant.blank?

    Apartment::Tenant.switch! tenant

    BulkSavingAnswer.bulk_saving.where(status: %i[created processing], created_at: 1.day.ago..(Time.current - 2.hours)).find_each do |bulk_saving_answer|
      answer_processings = bulk_saving_answer.answer_processings

      next if answer_processings.count == answer_processings.where(status: %i[done failed]).count

      BulkAnswerService.new(user_id: fourmdg_user.try(:id)).process_orphans(bulk_saving_answer.id)
    end
  end

  private

  def fourmdg_user
    User.find_by(email: REPROCESS_USER_EMAIL)
  end
end
