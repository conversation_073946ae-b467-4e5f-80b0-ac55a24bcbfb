# frozen_string_literal: true
require_relative '../../lib/elasticsearch/constants'

class CheckOutdatedContentsInElasticsearchForTenantWorker
  include Sidekiq::Worker
  include Elasticsearch::Constants

  BATCH_SIZE = 4000

  sidekiq_options queue: :elasticsearch_setup, lock: :until_executed, lock_ttl: 6.hours

  def perform(tenant, last_checked_elasticsearch = nil)
    Apartment::Tenant.switch!(tenant)
    processing_started_at = Time.now.iso8601
    process_businesses(tenant, last_checked_elasticsearch)
    Company.find_by(subdomain: tenant).update!(last_checked_elasticsearch: processing_started_at)
  end

  private

  def service
    @service ||= ElasticSearcherService.new
  end

  def process_businesses(tenant, last_checked_elasticsearch)
    Business.kept.where(integrate_elastic: true).find_each do |business|
      index_name = "#{tenant}-#{business.id}"

      unless index_exists?(index_name)
        Elasticsearch::BusinessSetupWorker.perform_async(tenant, business.id)
        next
      end

      contents_to_search = Content.unscoped.where(business: business, draft: false)
      if last_checked_elasticsearch.present?
        contents_to_search = contents_to_search.where(updated_at: last_checked_elasticsearch)
      end

      contents_to_search.find_in_batches(batch_size: BATCH_SIZE) do |contents|
        search_outdated_contents(business, contents)
      end
    end
  end

  def search_outdated_contents(business, contents)
    tenant = Apartment::Tenant.current

    content_ids = contents.pluck(:id)
    content_data = contents.pluck(:id, :updated_at)

    response = search_in_elasticsearch(content_ids, tenant, business.id)
    return unless response

    elasticsearch_contents = response['hits']['hits'].each_with_object({}) do |hit, hash|
      content_id = hit['fields'][CONTENT_ID]&.first
      updated_at = hit['fields'][UPDATED_AT]&.first
      hash[content_id] = updated_at
    end

    ids_to_reindex = content_data.each_with_object([]) do |(content_id, updated_at), array|
      missing = elasticsearch_contents[content_id].nil?
      outdated = elasticsearch_contents[content_id] != updated_at.utc.strftime("%Y-%m-%dT%H:%M:%S.000Z")
      array << content_id if missing || outdated
    end

    enqueue_missing_contents(tenant, ids_to_reindex) if ids_to_reindex.any?
  end

  def search_in_elasticsearch(content_ids, tenant, business_id)
    index_name = "#{tenant}-#{business_id}"

    service.client.search(
      index: index_name,
      body: {
        query: { bool: { must: [{ terms: { "#{CONTENT_ID}.keyword": content_ids } }] } },
        fields: [CONTENT_ID, UPDATED_AT], _source: false, size: BATCH_SIZE
      }
    )
  end

  def enqueue_missing_contents(tenant, content_ids)
    Elasticsearch::PutDocumentWorker.perform_bulk(content_ids.map { |id| [tenant, id] })
  end

  def index_exists?(index_name)
    service.client.indices.exists?(index: index_name)
  rescue StandardError => e
    false
  end
end
