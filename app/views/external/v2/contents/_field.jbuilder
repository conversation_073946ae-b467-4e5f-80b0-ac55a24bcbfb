json.extract! field, :id, :type, :size, :height, :tooltip, :order, :required,
              :enabled, :visible, :label, :input_variable, :output_variable,
              :show_on_form

json.reference_business_guid field.reference_business_id
json.reference_field_guid field.reference_field_id
json.reference_sub_business_guid field.reference_sub_business_id
json.options field.options if field.dropdown?

if field.sub_business? && field.reference_sub_business
  sub_contents = field.reference_sub_business.contents
                      .includes(answers: :step)
                      .where(parent_id: content.id, draft: false)

  json.value do
    json.contents sub_contents do |sub_content|
      json.content_id sub_content.id
      json.updated_at sub_content.updated_at&.iso8601

      json.steps sub_content.steps do |sub_step|
        json.id sub_step.id
        json.name sub_step.name
        answer = sub_content.answers.find_by(step_id: sub_step.id)
        json.updated_at answer&.updated_at&.iso8601

        json.templates sub_step.templates do |sub_template|
          json.id sub_template.id
          json.variable sub_template.variable

          json.fields sub_template.fields do |sub_field|
            json.partial! 'external/v2/contents/field', field: sub_field, content: sub_content, step: sub_step
          end
        end
      end
    end
  end
else
  if step && content
    answer = content.answers.find_by(step_id: step.id)
    json.value answer&.values&.dig(field.id)
  end
end