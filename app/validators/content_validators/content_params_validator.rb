module ContentValidators
  class ContentParamsValidator
    def initialize(params, base_fields)
      @params = params.permit!
      @base_fields = base_fields
    end

    def perform
      sanitize_params
    end

    private

    def sanitize_params
      base_params = @params.slice(*@base_fields)
      allowed_params = @params.slice(*allowed_fields)
      base_params.merge(allowed_params)
    end

    def allowed_fields
      business = Business.find_by(id: @params[:business_id])

      return [] if business.blank? || business.steps.empty?

      Template.joins(:step_templates).where(step_templates: { step_id: business.steps.pluck(:id) }).map do |template|
        template.fields.kept.map do |field|
          field['id'].to_sym
        end
      end.flatten
    end
  end
end